<?php

declare(strict_types=1);

namespace Bgs\LandingPages\Preview;

use Bgs\LandingPages\Service\UrlGenerationService;
use TYPO3\CMS\Backend\Routing\PreviewUriBuilder;
use TYPO3\CMS\Core\Database\ConnectionPool;
use TYPO3\CMS\Core\Site\SiteFinder;
use TYPO3\CMS\Core\Utility\GeneralUtility;
use TYPO3\CMS\Frontend\Preview\PreviewUriBuilderInterface;

/**
 * Preview Link Provider for Flight Routes
 *
 * This class generates preview URLs for flight routes that point to their virtual routes
 * instead of the parent landing page.
 */
class FlightRoutePreviewLinkProvider implements PreviewUriBuilderInterface
{
    protected UrlGenerationService $urlGenerationService;
    protected SiteFinder $siteFinder;

    public function __construct(
        ?UrlGenerationService $urlGenerationService = null,
        ?SiteFinder $siteFinder = null
    ) {
        $this->siteFinder = $siteFinder ?? GeneralUtility::makeInstance(SiteFinder::class);
        $this->urlGenerationService = $urlGenerationService ?? new UrlGenerationService($this->siteFinder);
    }

    /**
     * Build preview URI for a flight route record
     *
     * @param array $parameters Parameters containing the record data
     * @return string|null The preview URL or null if it cannot be generated
     */
    public function buildUriForElement(string $table, int $uid, array $context, string $targetUrl = null): ?string
    {
        // Only handle flight route records
        if ($table !== 'tx_landingpages_domain_model_flightroute') {
            return null;
        }

        try {
            // Get flight route data
            $flightRoute = $this->getFlightRouteData($uid);
            if (!$flightRoute) {
                return null;
            }

            // Get landing page ID (parent page)
            $landingPageId = (int)$flightRoute['pid'];
            if ($landingPageId === 0) {
                return null;
            }

            // Check if the landing page is a flight landing page (doktype 201)
            $landingPageInfo = $this->getPageInfo($landingPageId);
            if (!$landingPageInfo || (int)$landingPageInfo['doktype'] !== 201) {
                // Fall back to parent page if not a flight landing page
                return $this->buildParentPageUrl($landingPageId);
            }

            // Get route slug
            $routeSlug = $flightRoute['route_slug'] ?? '';
            if (empty($routeSlug)) {
                // Fall back to parent page if no route slug
                return $this->buildParentPageUrl($landingPageId);
            }

            // Generate virtual route URL
            $virtualRouteUrl = $this->urlGenerationService->generateUrlByIds($landingPageId, $routeSlug);
            
            return !empty($virtualRouteUrl) ? $virtualRouteUrl : $this->buildParentPageUrl($landingPageId);

        } catch (\Exception $e) {
            // Log error and fall back to parent page
            error_log('FlightRoutePreviewLinkProvider error: ' . $e->getMessage());
            
            // Try to get parent page as fallback
            try {
                $flightRoute = $this->getFlightRouteData($uid);
                if ($flightRoute) {
                    return $this->buildParentPageUrl((int)$flightRoute['pid']);
                }
            } catch (\Exception $fallbackException) {
                // Ignore fallback errors
            }
            
            return null;
        }
    }

    /**
     * Get flight route data from database
     */
    protected function getFlightRouteData(int $uid): ?array
    {
        $queryBuilder = GeneralUtility::makeInstance(ConnectionPool::class)
            ->getQueryBuilderForTable('tx_landingpages_domain_model_flightroute');

        $result = $queryBuilder
            ->select('*')
            ->from('tx_landingpages_domain_model_flightroute')
            ->where(
                $queryBuilder->expr()->eq('uid', $queryBuilder->createNamedParameter($uid, \PDO::PARAM_INT)),
                $queryBuilder->expr()->eq('deleted', $queryBuilder->createNamedParameter(0, \PDO::PARAM_INT))
            )
            ->executeQuery()
            ->fetchAssociative();

        return $result ?: null;
    }

    /**
     * Get page information
     */
    protected function getPageInfo(int $pageId): ?array
    {
        $queryBuilder = GeneralUtility::makeInstance(ConnectionPool::class)
            ->getQueryBuilderForTable('pages');

        $result = $queryBuilder
            ->select('uid', 'pid', 'title', 'doktype', 'slug')
            ->from('pages')
            ->where(
                $queryBuilder->expr()->eq('uid', $queryBuilder->createNamedParameter($pageId, \PDO::PARAM_INT)),
                $queryBuilder->expr()->eq('deleted', $queryBuilder->createNamedParameter(0, \PDO::PARAM_INT))
            )
            ->executeQuery()
            ->fetchAssociative();

        return $result ?: null;
    }

    /**
     * Build URL for parent page as fallback
     */
    protected function buildParentPageUrl(int $pageId): ?string
    {
        try {
            $previewUriBuilder = GeneralUtility::makeInstance(PreviewUriBuilder::class);
            $uri = $previewUriBuilder->buildUriForPage($pageId);
            return (string)$uri;
        } catch (\Exception $e) {
            return null;
        }
    }
}
