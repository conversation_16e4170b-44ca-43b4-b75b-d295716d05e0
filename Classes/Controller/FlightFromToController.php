<?php
namespace Bgs\LandingPages\Controller;

use TYPO3\CMS\Extbase\Mvc\Controller\ActionController;
use TYPO3\CMS\Core\Service\FlexFormService;
use TYPO3\CMS\Core\Utility\GeneralUtility;
use Psr\Http\Message\ResponseInterface;
use Bgs\LandingPages\Service\VirtualRouteService;

/**
 * Controller for FlightFromTo plugin
 */
class FlightFromToController extends ActionController
{
    protected VirtualRouteService $virtualRouteService;

    public function __construct(VirtualRouteService $virtualRouteService)
    {
        $this->virtualRouteService = $virtualRouteService;
    }

    /**
     * Show action - displays the from/to lists
     */
    public function showAction(): ResponseInterface
    {
        $flexFormService = GeneralUtility::makeInstance(FlexFormService::class);
        $contentObject = $this->configurationManager->getContentObject();
        $flexFormData = $flexFormService->convertFlexFormContentToArray($contentObject->data['pi_flexform'] ?? '');

        $fromListRaw = $flexFormData['from_list'] ?? '';
        $toListRaw = $flexFormData['to_list'] ?? '';
        $fromTitle = $flexFormData['from_title'] ?? '';
        $fromItem = $flexFormData['from_item'] ?? '';
        $fromEndpoint = $flexFormData['from_endpoint'] ?? '';
        $fromHref = $flexFormData['from_href'] ?? '';
        $toTitle = $flexFormData['to_title'] ?? '';
        $toItem = $flexFormData['to_item'] ?? '';
        $toEndpoint = $flexFormData['to_endpoint'] ?? '';
        $toHref = $flexFormData['to_href'] ?? '';

        $fromList = $this->parseCodeNameList($fromListRaw);
        $toList = $this->parseCodeNameList($toListRaw);
        $airportData = $this->getAirportDataFromVirtualRoute();

        $fromList = $this->processItemPlaceholders($fromList, $fromItem, $fromEndpoint, $fromHref, $airportData);
        $toList = $this->processItemPlaceholders($toList, $toItem, $toEndpoint, $toHref, $airportData);
        $this->view->assignMultiple([
            'fromList' => $fromList,
            'toList' => $toList,
            'hasFromList' => !empty($fromList),
            'hasToList' => !empty($toList),
            'hasAnyList' => !empty($fromList) || !empty($toList),
            'fromTitle' => $fromTitle,
            'fromItem' => $fromItem,
            'fromEndpoint' => $fromEndpoint,
            'fromHref' => $fromHref,
            'toTitle' => $toTitle,
            'toItem' => $toItem,
            'toEndpoint' => $toEndpoint,
            'toHref' => $toHref,
            'airport' => $airportData
        ]);

        return $this->htmlResponse();
    }

    /**
     * Parse code-name list from textarea input (format: "CODE - Name" per line)
     */
    protected function parseCodeNameList(string $listText): array
    {
        $items = [];

        if (empty(trim($listText))) {
            return $items;
        }

        $lines = explode("\n", $listText);

        foreach ($lines as $line) {
            $line = trim($line);

            if (empty($line)) {
                continue;
            }

            if (strpos($line, ' - ') !== false) {
                $parts = explode(' - ', $line, 2);
                $code = trim($parts[0]);
                $name = trim($parts[1]);

                if (!empty($code) && !empty($name)) {
                    $items[] = [
                        'code' => $code,
                        'name' => $name,
                        'display' => $code . ' - ' . $name
                    ];
                }
            } else {
                $items[] = [
                    'code' => $line,
                    'name' => '',
                    'display' => $line
                ];
            }
        }

        return $items;
    }

    /**
     * Process placeholders in item templates
     */
    protected function processItemPlaceholders(array $items, string $itemTemplate, string $endpointTemplate, string $hrefTemplate, array $airportData = []): array
    {
        if (empty($itemTemplate) && empty($endpointTemplate) && empty($hrefTemplate)) {
            return $items;
        }

        foreach ($items as &$item) {
            if (!empty($itemTemplate)) {
                $item['processedItem'] = $this->replacePlaceholdersInTemplate($itemTemplate, $item, $airportData);
            }
            if (!empty($endpointTemplate)) {
                $item['processedEndpoint'] = $this->replacePlaceholdersInTemplate($endpointTemplate, $item, $airportData);
            }
            if (!empty($hrefTemplate)) {
                $item['processedHref'] = $this->replacePlaceholdersInTemplate($hrefTemplate, $item, $airportData);
            }
        }

        return $items;
    }

    /**
     * Replace placeholders in template string
     */
    protected function replacePlaceholdersInTemplate(string $template, array $item, array $airportData = []): string
    {
        return str_replace(
            ['{item.code}', '{item.name}', '{airport.code}', '{airport.name}'],
            [$item['code'] ?? '', $item['name'] ?? '', $airportData['code'] ?? '', $airportData['name'] ?? ''],
            $template
        );
    }

    /**
     * Get airport data from virtual route based on origin/destination types
     */
    protected function getAirportDataFromVirtualRoute(): array
    {
        $airportData = ['code' => '', 'name' => ''];

        if (!$this->virtualRouteService->isVirtualRoute()) {
            return $airportData;
        }

        $virtualRouteData = $this->virtualRouteService->getCurrentVirtualRoute();
        if (!$virtualRouteData || !isset($virtualRouteData['flightRoute'])) {
            return $airportData;
        }

        $flightRoute = $virtualRouteData['flightRoute'];
        $sourceType = $flightRoute['origin_type'] ?? '';
        $destinationType = $flightRoute['destination_type'] ?? '';

        if ($sourceType === 'airport') {
            $airportData['code'] = $flightRoute['origin_code'] ?? '';
            $airportData['name'] = $flightRoute['origin_name'] ?? '';
        } elseif ($sourceType !== 'airport' && $destinationType === 'airport') {
            $airportData['code'] = $flightRoute['destination_code'] ?? '';
            $airportData['name'] = $flightRoute['destination_name'] ?? '';
        }

        return $airportData;
    }
}
