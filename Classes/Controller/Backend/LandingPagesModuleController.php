<?php

declare(strict_types=1);

namespace Bgs\LandingPages\Controller\Backend;

use Bgs\LandingPages\Domain\Repository\FlightRouteRepository;
use Bgs\LandingPages\Service\VirtualRouteService;
use Psr\Http\Message\ResponseInterface;
use Psr\Http\Message\ServerRequestInterface;
use TYPO3\CMS\Backend\Attribute\AsController;
use TYPO3\CMS\Backend\Routing\UriBuilder;
use TYPO3\CMS\Backend\Template\Components\ButtonBar;
use TYPO3\CMS\Backend\Template\ModuleTemplateFactory;
use TYPO3\CMS\Core\Imaging\IconFactory;
use TYPO3\CMS\Core\Page\PageRenderer;

/**
 * Backend Module Controller for Landing Pages
 */
#[AsController]
class LandingPagesModuleController
{
    public function __construct(
        private readonly ModuleTemplateFactory $moduleTemplateFactory,
        private readonly FlightRouteRepository $flightRouteRepository,
        private readonly VirtualRouteService $virtualRouteService,
        private readonly PageRenderer $pageRenderer,
        private readonly IconFactory $iconFactory,
        private readonly UriBuilder $uriBuilder
    ) {
    }



    /**
     * Main module index action
     */
    public function indexAction(ServerRequestInterface $request): ResponseInterface
    {
        $moduleTemplate = $this->moduleTemplateFactory->create($request);
        $moduleTemplate->setTitle('Landing Pages');

        // Add navigation buttons
        $this->addNavigationButtons($moduleTemplate, 'index');

        // Add some basic statistics
        $statistics = $this->getStatistics();

        $moduleTemplate->assignMultiple([
            'statistics' => $statistics,
            'extensionVersion' => $this->getExtensionVersion(),
        ]);

        return $moduleTemplate->renderResponse('LandingPagesModule/Index');
    }

    /**
     * Overview action showing detailed information
     */
    public function overviewAction(ServerRequestInterface $request): ResponseInterface
    {
        $moduleTemplate = $this->moduleTemplateFactory->create($request);
        $moduleTemplate->setTitle('Landing Pages - Overview');

        // Add navigation buttons
        $this->addNavigationButtons($moduleTemplate, 'overview');

        // Get detailed information
        $flightRoutes = $this->flightRouteRepository->findAll();
        $statistics = $this->getStatistics();

        $moduleTemplate->assignMultiple([
            'flightRoutes' => $flightRoutes,
            'statistics' => $statistics,
            'extensionVersion' => $this->getExtensionVersion(),
        ]);

        return $moduleTemplate->renderResponse('LandingPagesModule/Overview');
    }

    /**
     * Get basic statistics for the module
     */
    private function getStatistics(): array
    {
        $totalRoutes = $this->flightRouteRepository->countAll();
        $activeRoutes = $this->flightRouteRepository->countByActive(true);
        
        return [
            'totalRoutes' => $totalRoutes,
            'activeRoutes' => $activeRoutes,
            'inactiveRoutes' => $totalRoutes - $activeRoutes,
        ];
    }

    /**
     * Get extension version from ext_emconf.php
     */
    private function getExtensionVersion(): string
    {
        $extEmconfPath = \TYPO3\CMS\Core\Utility\ExtensionManagementUtility::extPath('landing-pages') . 'ext_emconf.php';
        if (file_exists($extEmconfPath)) {
            $EM_CONF = [];
            include $extEmconfPath;
            return $EM_CONF['landing-pages']['version'] ?? '1.0.0';
        }
        return '1.0.0';
    }

    /**
     * Add navigation buttons to the module template
     */
    private function addNavigationButtons($moduleTemplate, string $currentAction): void
    {
        $buttonBar = $moduleTemplate->getDocHeaderComponent()->getButtonBar();

        // Dashboard button
        $dashboardButton = $buttonBar->makeLinkButton()
            ->setTitle('Dashboard')
            ->setIcon($this->iconFactory->getIcon('actions-home', 'small'))
            ->setHref($this->uriBuilder->buildUriFromRoute('web_landingpages'));

        if ($currentAction === 'index') {
            $dashboardButton->setClasses('active');
        }

        // Overview button
        $overviewButton = $buttonBar->makeLinkButton()
            ->setTitle('Overview')
            ->setIcon($this->iconFactory->getIcon('actions-list-alternative', 'small'))
            ->setHref($this->uriBuilder->buildUriFromRoute('web_landingpages.overview'));

        if ($currentAction === 'overview') {
            $overviewButton->setClasses('active');
        }

        $buttonBar->addButton($dashboardButton, ButtonBar::BUTTON_POSITION_LEFT);
        $buttonBar->addButton($overviewButton, ButtonBar::BUTTON_POSITION_LEFT);
    }
}
