<?php
namespace Bgs\LandingPages\EventListener;

use TYPO3\CMS\Backend\Controller\Event\ModifyPageLayoutContentEvent;
use TYPO3\CMS\Backend\Routing\UriBuilder;
use TYPO3\CMS\Core\Database\ConnectionPool;
use TYPO3\CMS\Core\Page\PageRenderer;
use TYPO3\CMS\Core\Site\SiteFinder;
use TYPO3\CMS\Core\Utility\GeneralUtility;
use TYPO3\CMS\Fluid\View\StandaloneView;
use Bgs\LandingPages\Domain\Repository\FlightRouteRepository;
use Bgs\LandingPages\Service\UrlGenerationService;

/**
 * Event listener for page layout content modification
 *
 * This listener adds template page information to the backend page layout
 * for Flight Landing pages (doktype 201)
 */
class PagePreviewEventListener
{
    protected FlightRouteRepository $flightRouteRepository;
    protected SiteFinder $siteFinder;
    protected UrlGenerationService $urlGenerationService;

    public function __construct(
        FlightRouteRepository $flightRouteRepository,
        SiteFinder $siteFinder,
        ?UrlGenerationService $urlGenerationService = null
    ) {
        $this->flightRouteRepository = $flightRouteRepository;
        $this->siteFinder = $siteFinder;
        $this->urlGenerationService = $urlGenerationService ?? new UrlGenerationService($siteFinder);
    }

    /**
     * Handle the page layout content modification event
     */
    public function __invoke(ModifyPageLayoutContentEvent $event): void
    {
        $request = $event->getRequest();
        $pageId = (int)($request->getQueryParams()['id'] ?? 0);

        if ($pageId === 0) {
            return;
        }

        // Get page information
        $pageInfo = $this->getPageInfo($pageId);
        if (!$pageInfo) {
            return;
        }

        // Only handle Flight Landing pages (doktype 201)
        if ((int)($pageInfo['doktype'] ?? 0) !== 201) {
            return;
        }

        // Check if template page is set
        $templatePageUid = (int)($pageInfo['tx_landingpages_template_page'] ?? 0);
        if ($templatePageUid === 0) {
            return;
        }

        // Get template page information
        $templatePageInfo = $this->getTemplatePageInfo($templatePageUid);
        if (!$templatePageInfo) {
            return;
        }

        // Get template mappings (variants) for this landing page
        $templateMappings = $this->getTemplateMappings($pageId);

        // Get flight routes for this landing page
        $flightRoutes = $this->getFlightRoutes($pageId);

        // Generate preview URLs for flight routes
        $flightRoutesWithUrls = $this->addPreviewUrlsToRoutes($flightRoutes, $pageId);

        // Generate URL for adding new flight route
        $addRouteUrl = $this->generateAddRouteUrl($pageId);

        // Generate URL for CSV export
        $csvExportUrl = $this->generateCsvExportUrl($pageId);

        // Generate URL for CSV import
        $csvImportUrl = $this->generateCsvImportUrl($pageId);

        // Generate URL for cache clearing
        $clearCacheUrl = $this->generateClearCacheUrl($pageId);

        // Include backend CSS and JavaScript
        $this->includeBackendCss();
        $this->includeBackendJs();

        // Calculate route counts
        $totalRoutes = count($flightRoutesWithUrls);
        $activeRoutes = count(array_filter($flightRoutesWithUrls, function($route) {
            return $route['isActive'] ?? false;
        }));
        $inactiveRoutes = $totalRoutes - $activeRoutes;

        // Render the preview content
        $previewContent = $this->renderPreviewContent($templatePageInfo, $pageInfo, $flightRoutesWithUrls, $templateMappings, $addRouteUrl, $csvExportUrl, $csvImportUrl, $clearCacheUrl, $totalRoutes, $activeRoutes, $inactiveRoutes);

        // Add to the header content
        $event->addHeaderContent($previewContent);
    }

    /**
     * Include backend CSS for the preview
     */
    protected function includeBackendCss(): void
    {
        $pageRenderer = GeneralUtility::makeInstance(PageRenderer::class);
        $pageRenderer->addCssFile('EXT:landing-pages/Resources/Public/CSS/backend.css');
    }

    /**
     * Include backend JavaScript for the preview
     */
    protected function includeBackendJs(): void
    {
        $pageRenderer = GeneralUtility::makeInstance(PageRenderer::class);
        $pageRenderer->addJsFile('EXT:landing-pages/Resources/Public/JavaScript/backend.js');
    }

    /**
     * Get page information
     */
    protected function getPageInfo(int $pageId): ?array
    {
        $connectionPool = GeneralUtility::makeInstance(ConnectionPool::class);
        $connection = $connectionPool->getConnectionForTable('pages');

        $page = $connection->select(
            ['uid', 'title', 'doktype', 'tx_landingpages_template_page'],
            'pages',
            [
                'uid' => $pageId,
                'deleted' => 0
            ]
        )->fetchAssociative();

        return $page ?: null;
    }

    /**
     * Get template page information
     */
    protected function getTemplatePageInfo(int $templatePageUid): ?array
    {
        $connectionPool = GeneralUtility::makeInstance(ConnectionPool::class);
        $connection = $connectionPool->getConnectionForTable('pages');

        $templatePage = $connection->select(
            ['uid', 'title', 'doktype', 'hidden', 'deleted'],
            'pages',
            [
                'uid' => $templatePageUid,
                'deleted' => 0
            ]
        )->fetchAssociative();

        return $templatePage ?: null;
    }

    /**
     * Get template mappings (variants) for the landing page
     */
    protected function getTemplateMappings(int $landingPageUid): array
    {
        $queryBuilder = GeneralUtility::makeInstance(ConnectionPool::class)
            ->getQueryBuilderForTable('tx_landingpages_domain_model_templatemapping');

        $mappings = $queryBuilder
            ->select('tm.uid', 'tm.origin_type', 'tm.destination_type', 'tm.template_page_uid', 'p.title as template_title', 'p.slug as template_slug', 'p.hidden as template_hidden')
            ->from('tx_landingpages_domain_model_templatemapping', 'tm')
            ->leftJoin('tm', 'pages', 'p', 'tm.template_page_uid = p.uid AND p.deleted = 0')
            ->where(
                $queryBuilder->expr()->eq('tm.landing_page_uid', $queryBuilder->createNamedParameter($landingPageUid, \PDO::PARAM_INT)),
                $queryBuilder->expr()->eq('tm.deleted', 0),
                $queryBuilder->expr()->eq('tm.hidden', 0)
            )
            ->orderBy('tm.origin_type')
            ->addOrderBy('tm.destination_type')
            ->executeQuery()
            ->fetchAllAssociative();

        return $mappings ?: [];
    }

    /**
     * Get flight routes for the landing page (including inactive ones for backend preview)
     */
    protected function getFlightRoutes(int $landingPageUid): array
    {
        try {
            return $this->flightRouteRepository->findAllByLandingPage($landingPageUid);
        } catch (\Exception $e) {
            // Return empty array if there's an error fetching routes
            return [];
        }
    }

    /**
     * Add preview URLs and edit URLs to flight routes
     */
    protected function addPreviewUrlsToRoutes(array $flightRoutes, int $landingPageUid): array
    {
        try {
            $routesWithUrls = [];
            foreach ($flightRoutes as $route) {
                if ($route instanceof \Bgs\LandingPages\Domain\Model\FlightRoute) {
                    // Convert domain model to array for template usage
                    $routeArray = [
                        'uid' => $route->getUid(),
                        'originCode' => $route->getOriginCode(),
                        'originName' => $route->getOriginName(),
                        'originType' => $route->getOriginType(),
                        'destinationCode' => $route->getDestinationCode(),
                        'destinationName' => $route->getDestinationName(),
                        'destinationType' => $route->getDestinationType(),
                        'routeSlug' => $route->getRouteSlug(),
                        'isActive' => $route->getIsActive(),
                    ];
                    $routeSlug = $route->getRouteSlug();
                } else {
                    // Handle array format (from repository)
                    $routeArray = $route;
                    $routeSlug = $route['routeSlug'] ?? $route['route_slug'] ?? '';
                }

                if (!empty($routeSlug)) {
                    // Generate fully qualified preview URL using UrlGenerationService
                    $routeArray['previewUrl'] = $this->urlGenerationService->generateUrlByIds($landingPageUid, $routeSlug);
                } else {
                    $routeArray['previewUrl'] = '';
                }

                // Generate edit URL for this route
                $editUrl = $this->generateEditRouteUrl($routeArray['uid']);
                $routeArray['editUrl'] = $editUrl;

                $routesWithUrls[] = $routeArray;
            }

            return $routesWithUrls;
        } catch (\Exception $e) {
            // Return routes without preview URLs if there's an error
            // Convert objects to arrays for template compatibility
            $routesArray = [];
            foreach ($flightRoutes as $route) {
                if ($route instanceof \Bgs\LandingPages\Domain\Model\FlightRoute) {
                    $routesArray[] = [
                        'uid' => $route->getUid(),
                        'originCode' => $route->getOriginCode(),
                        'originName' => $route->getOriginName(),
                        'originType' => $route->getOriginType(),
                        'destinationCode' => $route->getDestinationCode(),
                        'destinationName' => $route->getDestinationName(),
                        'destinationType' => $route->getDestinationType(),
                        'routeSlug' => $route->getRouteSlug(),
                        'isActive' => $route->getIsActive(),
                        'previewUrl' => '',
                        'editUrl' => '',
                    ];
                } else {
                    $route['previewUrl'] = '';
                    $route['editUrl'] = '';
                    $routesArray[] = $route;
                }
            }
            return $routesArray;
        }
    }

    /**
     * Generate URL for adding new flight route record
     */
    protected function generateAddRouteUrl(int $landingPageUid): string
    {
        try {
            $uriBuilder = GeneralUtility::makeInstance(UriBuilder::class);

            // Build URL for creating a new flight route record
            $parameters = [
                'edit' => [
                    'tx_landingpages_domain_model_flightroute' => [
                        $landingPageUid => 'new'
                    ]
                ],
                'defVals' => [
                    'tx_landingpages_domain_model_flightroute' => [
                        'is_active' => 1
                    ]
                ],
                'returnUrl' => GeneralUtility::getIndpEnv('REQUEST_URI')
            ];

            return (string)$uriBuilder->buildUriFromRoute('record_edit', $parameters);
        } catch (\Exception $e) {
            // Return empty string if URL generation fails
            return '';
        }
    }

    /**
     * Generate URL for editing existing flight route record
     */
    protected function generateEditRouteUrl(int $routeUid): string
    {
        try {
            $uriBuilder = GeneralUtility::makeInstance(UriBuilder::class);

            // Build URL for editing an existing flight route record
            $parameters = [
                'edit' => [
                    'tx_landingpages_domain_model_flightroute' => [
                        $routeUid => 'edit'
                    ]
                ],
                'returnUrl' => GeneralUtility::getIndpEnv('REQUEST_URI')
            ];

            return (string)$uriBuilder->buildUriFromRoute('record_edit', $parameters);
        } catch (\Exception $e) {
            // Return empty string if URL generation fails
            return '';
        }
    }

    /**
     * Generate URL for CSV export
     */
    protected function generateCsvExportUrl(int $landingPageUid): string
    {
        try {
            $uriBuilder = GeneralUtility::makeInstance(UriBuilder::class);

            // Build URL for CSV export
            $parameters = [
                'landingPageId' => $landingPageUid
            ];

            return (string)$uriBuilder->buildUriFromRoute('flight_landing_pages_csv_export', $parameters);
        } catch (\Exception $e) {
            // Return empty string if URL generation fails
            return '';
        }
    }

    /**
     * Generate URL for CSV import form
     */
    protected function generateCsvImportUrl(int $landingPageUid): string
    {
        try {
            $uriBuilder = GeneralUtility::makeInstance(UriBuilder::class);

            // Build URL for CSV import form
            $parameters = [
                'landingPageId' => $landingPageUid,
                'returnUrl' => GeneralUtility::getIndpEnv('REQUEST_URI')
            ];

            return (string)$uriBuilder->buildUriFromRoute('flight_landing_pages_csv_import_form', $parameters);
        } catch (\Exception $e) {
            // Return empty string if URL generation fails
            return '';
        }
    }

    /**
     * Generate URL for clearing template cache
     */
    protected function generateClearCacheUrl(int $landingPageUid): string
    {
        try {
            $uriBuilder = GeneralUtility::makeInstance(UriBuilder::class);

            // Build URL for cache clearing
            $parameters = [
                'landingPageId' => $landingPageUid
            ];

            return (string)$uriBuilder->buildUriFromRoute('flight_landing_pages_clear_template_cache', $parameters);
        } catch (\Exception $e) {
            // Return empty string if URL generation fails
            return '';
        }
    }

    /**
     * Render the preview content using Fluid template
     */
    protected function renderPreviewContent(array $templatePageInfo, array $landingPageInfo, array $flightRoutes, array $templateMappings, string $addRouteUrl, string $csvExportUrl = '', string $csvImportUrl = '', string $clearCacheUrl = '', int $totalRoutes = 0, int $activeRoutes = 0, int $inactiveRoutes = 0): string
    {
        $view = GeneralUtility::makeInstance(StandaloneView::class);
        $view->setTemplatePathAndFilename(
            GeneralUtility::getFileAbsFileName(
                'EXT:landing-pages/Resources/Private/Templates/Backend/PagePreview.html'
            )
        );

        $view->assignMultiple([
            'templatePage' => $templatePageInfo,
            'landingPage' => $landingPageInfo,
            'flightRoutes' => $flightRoutes,
            'templateMappings' => $templateMappings,
            'routeCount' => $totalRoutes,
            'activeRouteCount' => $activeRoutes,
            'inactiveRouteCount' => $inactiveRoutes,
            'addRouteUrl' => $addRouteUrl,
            'csvExportUrl' => $csvExportUrl,
            'csvImportUrl' => $csvImportUrl,
            'clearCacheUrl' => $clearCacheUrl,
            'isTemplatePageHidden' => (bool)($templatePageInfo['hidden'] ?? false),
            'isTemplatePageTemplate' => (int)($templatePageInfo['doktype'] ?? 0) === 200
        ]);

        return $view->render();
    }
}
