<?php

declare(strict_types=1);

namespace Bgs\LandingPages\EventListener;

use Bgs\LandingPages\Service\UrlGenerationService;
use TYPO3\CMS\Backend\Routing\Event\BeforePagePreviewUriGeneratedEvent;
use TYPO3\CMS\Core\Database\ConnectionPool;
use TYPO3\CMS\Core\Http\Uri;
use TYPO3\CMS\Core\Site\SiteFinder;
use TYPO3\CMS\Core\Utility\GeneralUtility;

/**
 * Event listener to modify preview URIs for flight route edit forms
 *
 * This listener modifies the preview URI for flight routes to point to their virtual route
 * instead of the parent page when the "View" button is clicked in the backend edit form.
 */
class ModifyFlightRoutePreviewUriEventListener
{
    protected UrlGenerationService $urlGenerationService;
    protected SiteFinder $siteFinder;

    public function __construct(
        ?UrlGenerationService $urlGenerationService = null,
        ?SiteFinder $siteFinder = null
    ) {
        $this->siteFinder = $siteFinder ?? GeneralUtility::makeInstance(SiteFinder::class);
        $this->urlGenerationService = $urlGenerationService ?? new UrlGenerationService($this->siteFinder);
    }

    /**
     * Modify the preview URI for flight route edit forms
     */
    public function __invoke(BeforePagePreviewUriGeneratedEvent $event): void
    {
        // Check if we're dealing with a flight route record
        $flightRouteUid = $this->getFlightRouteUidFromContext($event);
        if ($flightRouteUid === 0) {
            return;
        }

        // Get flight route data
        $flightRoute = $this->getFlightRouteData($flightRouteUid);
        if (!$flightRoute) {
            return;
        }

        // Generate virtual route URL
        $virtualRouteUrl = $this->generateVirtualRouteUrl($flightRoute);
        if (empty($virtualRouteUrl)) {
            return;
        }

        // Set the custom preview URI and stop propagation
        $event->setPreviewUri(new Uri($virtualRouteUrl));
    }

    /**
     * Get the flight route UID from the event context
     */
    protected function getFlightRouteUidFromContext(BeforePagePreviewUriGeneratedEvent $event): int
    {
        // The preview URI generation is triggered when viewing a record
        // We need to check if the current context is for a flight route record

        // Get the page ID from the event - this might be the flight route record's PID
        $pageId = $event->getPageId();

        // Check if we have additional query parameters that might contain record information
        $additionalParams = $event->getAdditionalQueryParameters();

        // Look for edit parameters in the query
        if (isset($additionalParams['edit']['tx_landingpages_domain_model_flightroute'])) {
            $editConfig = $additionalParams['edit']['tx_landingpages_domain_model_flightroute'];
            foreach ($editConfig as $uid => $action) {
                if ($action === 'edit' && is_numeric($uid)) {
                    return (int)$uid;
                }
            }
        }

        // Alternative: Check if we can get the record UID from the current backend context
        // This might be available in the request or context
        $request = $GLOBALS['TYPO3_REQUEST'] ?? null;
        if ($request) {
            $queryParams = $request->getQueryParams();
            if (isset($queryParams['edit']['tx_landingpages_domain_model_flightroute'])) {
                $editConfig = $queryParams['edit']['tx_landingpages_domain_model_flightroute'];
                foreach ($editConfig as $uid => $action) {
                    if ($action === 'edit' && is_numeric($uid)) {
                        return (int)$uid;
                    }
                }
            }
        }

        return 0;
    }

    /**
     * Get flight route data from database
     */
    protected function getFlightRouteData(int $uid): ?array
    {
        $queryBuilder = GeneralUtility::makeInstance(ConnectionPool::class)
            ->getQueryBuilderForTable('tx_landingpages_domain_model_flightroute');

        $result = $queryBuilder
            ->select('*')
            ->from('tx_landingpages_domain_model_flightroute')
            ->where(
                $queryBuilder->expr()->eq('uid', $queryBuilder->createNamedParameter($uid, \PDO::PARAM_INT)),
                $queryBuilder->expr()->eq('deleted', $queryBuilder->createNamedParameter(0, \PDO::PARAM_INT))
            )
            ->executeQuery()
            ->fetchAssociative();

        return $result ?: null;
    }

    /**
     * Generate virtual route URL for the flight route
     */
    protected function generateVirtualRouteUrl(array $flightRoute): string
    {
        try {
            // Get landing page ID (parent page)
            $landingPageId = (int)$flightRoute['pid'];
            if ($landingPageId === 0) {
                return '';
            }

            // Check if the landing page is a flight landing page (doktype 201)
            $landingPageInfo = $this->getPageInfo($landingPageId);
            if (!$landingPageInfo || (int)$landingPageInfo['doktype'] !== 201) {
                return '';
            }

            // Get route slug
            $routeSlug = $flightRoute['route_slug'] ?? '';
            if (empty($routeSlug)) {
                return '';
            }

            // Generate virtual route URL
            return $this->urlGenerationService->generateUrlByIds($landingPageId, $routeSlug);

        } catch (\Exception $e) {
            // Log error and return empty string
            error_log('ModifyFlightRouteButtonBarEventListener error: ' . $e->getMessage());
            return '';
        }
    }

    /**
     * Get page information
     */
    protected function getPageInfo(int $pageId): ?array
    {
        $queryBuilder = GeneralUtility::makeInstance(ConnectionPool::class)
            ->getQueryBuilderForTable('pages');

        $result = $queryBuilder
            ->select('uid', 'pid', 'title', 'doktype', 'slug')
            ->from('pages')
            ->where(
                $queryBuilder->expr()->eq('uid', $queryBuilder->createNamedParameter($pageId, \PDO::PARAM_INT)),
                $queryBuilder->expr()->eq('deleted', $queryBuilder->createNamedParameter(0, \PDO::PARAM_INT))
            )
            ->executeQuery()
            ->fetchAssociative();

        return $result ?: null;
    }

}
