<?php

declare(strict_types=1);

namespace Bgs\LandingPages\Service;

use TYPO3\CMS\Core\Database\ConnectionPool;
use TYPO3\CMS\Core\Utility\GeneralUtility;

/**
 * Service for resolving template pages based on route type combinations
 */
class TemplateResolutionService
{
    public function __construct()
    {
        // No dependencies needed - using direct database access
    }

    /**
     * Resolve template page UID for a route type combination
     *
     * Priority:
     * 1. Specific type combination mapping
     * 2. Default template page
     */
    public function resolveTemplatePageForRoute(int $landingPageUid, string $originType, string $destinationType): int
    {
        // 1. Try to find specific mapping
        $mapping = $this->findTemplateMappingByLandingPageAndTypes($landingPageUid, $originType, $destinationType);
        if ($mapping && ($mapping['template_page_uid'] ?? 0) > 0) {
            return (int)$mapping['template_page_uid'];
        }

        // 2. Fall back to default template
        return $this->getDefaultTemplate($landingPageUid);
    }

    /**
     * Get default template page UID for landing page
     */
    public function getDefaultTemplate(int $landingPageUid): int
    {
        try {
            $queryBuilder = GeneralUtility::makeInstance(ConnectionPool::class)
                ->getQueryBuilderForTable('pages');

            $result = $queryBuilder
                ->select('tx_landingpages_template_page')
                ->from('pages')
                ->where(
                    $queryBuilder->expr()->eq('uid', $queryBuilder->createNamedParameter($landingPageUid, \PDO::PARAM_INT)),
                    $queryBuilder->expr()->eq('deleted', 0)
                )
                ->executeQuery()
                ->fetchAssociative();

            return (int)($result['tx_landingpages_template_page'] ?? 0);
        } catch (\Exception $e) {
            return 0;
        }
    }

    /**
     * Get all available type combinations
     */
    public function getAvailableTypeCombinations(): array
    {
        return [
            'airport' => ['airport', 'city', 'country'],
            'city' => ['airport', 'city', 'country'],
            'country' => ['airport', 'city', 'country']
        ];
    }

    /**
     * Get all mappings for a landing page
     */
    public function getMappingsForLandingPage(int $landingPageUid): array
    {
        return $this->findTemplateMappingsByLandingPage($landingPageUid);
    }

    /**
     * Find template mapping by landing page and route types
     */
    protected function findTemplateMappingByLandingPageAndTypes(int $landingPageUid, string $originType, string $destinationType): ?array
    {
        $queryBuilder = GeneralUtility::makeInstance(ConnectionPool::class)
            ->getQueryBuilderForTable('tx_landingpages_domain_model_templatemapping');

        $result = $queryBuilder
            ->select('*')
            ->from('tx_landingpages_domain_model_templatemapping')
            ->where(
                $queryBuilder->expr()->eq('landing_page_uid', $queryBuilder->createNamedParameter($landingPageUid, \PDO::PARAM_INT)),
                $queryBuilder->expr()->eq('origin_type', $queryBuilder->createNamedParameter($originType)),
                $queryBuilder->expr()->eq('destination_type', $queryBuilder->createNamedParameter($destinationType)),
                $queryBuilder->expr()->eq('deleted', 0),
                $queryBuilder->expr()->eq('hidden', 0)
            )
            ->executeQuery();

        return $result->fetchAssociative() ?: null;
    }

    /**
     * Find all mappings for a landing page
     */
    protected function findTemplateMappingsByLandingPage(int $landingPageUid): array
    {
        $queryBuilder = GeneralUtility::makeInstance(ConnectionPool::class)
            ->getQueryBuilderForTable('tx_landingpages_domain_model_templatemapping');

        $result = $queryBuilder
            ->select('*')
            ->from('tx_landingpages_domain_model_templatemapping')
            ->where(
                $queryBuilder->expr()->eq('landing_page_uid', $queryBuilder->createNamedParameter($landingPageUid, \PDO::PARAM_INT)),
                $queryBuilder->expr()->eq('deleted', 0),
                $queryBuilder->expr()->eq('hidden', 0)
            )
            ->orderBy('origin_type', 'ASC')
            ->addOrderBy('destination_type', 'ASC')
            ->executeQuery();

        return $result->fetchAllAssociative();
    }
}
