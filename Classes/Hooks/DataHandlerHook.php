<?php

declare(strict_types=1);

namespace Bgs\LandingPages\Hooks;

use Bgs\LandingPages\Service\SlugUpdateService;
use Bgs\LandingPages\Service\TemplateResolutionService;
use TYPO3\CMS\Core\Cache\CacheManager;
use TYPO3\CMS\Core\Database\ConnectionPool;
use TYPO3\CMS\Core\DataHandling\DataHandler;
use TYPO3\CMS\Core\Utility\GeneralUtility;

/**
 * DataHandler hook for automatic slug updates and cache clearing
 *
 * Handles:
 * - Automatic updates of flight route and landing page slugs when parent page slugs are changed
 * - Automatic cache clearing of template pages when flight routes are edited
 */
class DataHandlerHook
{
    protected SlugUpdateService $slugUpdateService;
    protected TemplateResolutionService $templateResolutionService;
    protected CacheManager $cacheManager;

    /**
     * Store old slugs before they are updated
     * @var array
     */
    protected array $oldSlugs = [];

    public function __construct()
    {
        $this->slugUpdateService = GeneralUtility::makeInstance(SlugUpdateService::class);
        $this->templateResolutionService = GeneralUtility::makeInstance(TemplateResolutionService::class);
        $this->cacheManager = GeneralUtility::makeInstance(CacheManager::class);
    }

    /**
     * Hook that is called before field processing to capture old slugs
     *
     * @param array $incomingFieldArray Field values being processed
     * @param string $table Table name
     * @param string|int $id Record ID
     * @param DataHandler $dataHandler DataHandler instance
     */
    public function processDatamap_preProcessFieldArray(
        array &$incomingFieldArray,
        string $table,
        $id,
        DataHandler $dataHandler
    ): void {
        // Only process page updates where slug is being changed
        if ($table !== 'pages' || !isset($incomingFieldArray['slug'])) {
            return;
        }

        $pageId = (int)$id;
        if ($pageId <= 0) {
            return;
        }

        // Store the old slug before it gets updated
        $oldSlug = $this->getPageSlug($pageId);
        if (!empty($oldSlug)) {
            $this->oldSlugs[$pageId] = $oldSlug;
        }
    }

    /**
     * Hook that is called after database operations
     *
     * @param string $status Operation status (new, update)
     * @param string $table Table name
     * @param string|int $id Record ID
     * @param array $fieldArray Field values
     * @param DataHandler $dataHandler DataHandler instance
     */
    public function processDatamap_afterDatabaseOperations(
        string $status,
        string $table,
        $id,
        array $fieldArray,
        DataHandler $dataHandler
    ): void {
        // Handle flight route updates for cache clearing
        if ($table === 'tx_landingpages_domain_model_flightroute') {
            $this->handleFlightRouteUpdate($status, (int)$id, $fieldArray);
            return;
        }

        // Only process page updates where slug has changed
        if ($table !== 'pages' || $status !== 'update' || !isset($fieldArray['slug'])) {
            return;
        }

        $pageId = (int)$id;
        if ($pageId <= 0) {
            return;
        }

        // Get the old and new slugs
        $oldSlug = $this->oldSlugs[$pageId] ?? '';
        $newSlug = $fieldArray['slug'];

        // Only proceed if slug actually changed
        if (empty($oldSlug) || $oldSlug === $newSlug) {
            // Clean up stored slug
            unset($this->oldSlugs[$pageId]);
            return;
        }

        // Check if this is a landing page (doktype 201) - if so, update its flight routes
        $pageInfo = $this->getPageInfo($pageId);
        if ($pageInfo && (int)$pageInfo['doktype'] === 201) {
            // This is a landing page - update its flight routes directly
            $routesUpdated = $this->slugUpdateService->updateFlightRouteSlugs($pageId);
        }

        // Update child landing pages and their flight routes (for any page type)
        $stats = $this->slugUpdateService->updateChildSlugs($pageId, $oldSlug, $newSlug);

        // Clean up stored slug
        unset($this->oldSlugs[$pageId]);

        // Log results if needed (could be extended with proper logging)
        if (!empty($stats['errors'])) {
            // Handle errors if needed
        }
    }

    /**
     * Get current slug for a page
     *
     * @param int $pageId Page ID
     * @return string Current slug
     */
    protected function getPageSlug(int $pageId): string
    {
        $queryBuilder = GeneralUtility::makeInstance(ConnectionPool::class)
            ->getQueryBuilderForTable('pages');

        $result = $queryBuilder
            ->select('slug')
            ->from('pages')
            ->where(
                $queryBuilder->expr()->eq('uid', $queryBuilder->createNamedParameter($pageId, \PDO::PARAM_INT)),
                $queryBuilder->expr()->eq('deleted', $queryBuilder->createNamedParameter(0, \PDO::PARAM_INT))
            )
            ->executeQuery()
            ->fetchAssociative();

        return $result['slug'] ?? '';
    }

    /**
     * Get page information including doktype
     *
     * @param int $pageId Page ID
     * @return array|null Page information or null if not found
     */
    protected function getPageInfo(int $pageId): ?array
    {
        $queryBuilder = GeneralUtility::makeInstance(ConnectionPool::class)
            ->getQueryBuilderForTable('pages');

        $result = $queryBuilder
            ->select('uid', 'pid', 'title', 'slug', 'doktype')
            ->from('pages')
            ->where(
                $queryBuilder->expr()->eq('uid', $queryBuilder->createNamedParameter($pageId, \PDO::PARAM_INT)),
                $queryBuilder->expr()->eq('deleted', $queryBuilder->createNamedParameter(0, \PDO::PARAM_INT))
            )
            ->executeQuery()
            ->fetchAssociative();

        return $result ?: null;
    }

    /**
     * Handle flight route updates and clear appropriate template page cache
     *
     * @param string $status Operation status (new, update)
     * @param int $routeId Flight route ID
     * @param array $fieldArray Field values
     */
    protected function handleFlightRouteUpdate(string $status, int $routeId, array $fieldArray): void
    {
        if ($routeId <= 0) {
            return;
        }

        try {
            // Get the flight route data (either from fieldArray for updates or from database for new records)
            $routeData = $this->getFlightRouteData($routeId, $fieldArray, $status);
            if (!$routeData) {
                return;
            }

            $landingPageUid = (int)$routeData['pid'];
            $originType = $routeData['origin_type'] ?? '';
            $destinationType = $routeData['destination_type'] ?? '';

            if ($landingPageUid <= 0 || empty($originType) || empty($destinationType)) {
                return;
            }

            // Resolve the template page for this route type combination
            $templatePageUid = $this->templateResolutionService->resolveTemplatePageForRoute(
                $landingPageUid,
                $originType,
                $destinationType
            );

            if ($templatePageUid > 0) {
                // Clear cache for the resolved template page
                $this->clearTemplatePageCache($templatePageUid);
            }

        } catch (\Exception $e) {
            // Log error but don't break the save operation
            // Could be extended with proper logging if needed
        }
    }

    /**
     * Get flight route data for cache clearing
     *
     * @param int $routeId Flight route ID
     * @param array $fieldArray Field values from DataHandler
     * @param string $status Operation status
     * @return array|null Route data or null if not found
     */
    protected function getFlightRouteData(int $routeId, array $fieldArray, string $status): ?array
    {
        // For new records, use the field array directly
        if ($status === 'new') {
            return $fieldArray;
        }

        // For updates, get current data from database and merge with updates
        $queryBuilder = GeneralUtility::makeInstance(ConnectionPool::class)
            ->getQueryBuilderForTable('tx_landingpages_domain_model_flightroute');

        $result = $queryBuilder
            ->select('pid', 'origin_type', 'destination_type')
            ->from('tx_landingpages_domain_model_flightroute')
            ->where(
                $queryBuilder->expr()->eq('uid', $queryBuilder->createNamedParameter($routeId, \PDO::PARAM_INT)),
                $queryBuilder->expr()->eq('deleted', $queryBuilder->createNamedParameter(0, \PDO::PARAM_INT))
            )
            ->executeQuery()
            ->fetchAssociative();

        if (!$result) {
            return null;
        }

        // Merge database data with any updates from fieldArray
        return array_merge($result, $fieldArray);
    }

    /**
     * Clear frontend cache for a specific template page
     *
     * @param int $templatePageUid Template page UID
     */
    protected function clearTemplatePageCache(int $templatePageUid): void
    {
        $pageCache = $this->cacheManager->getCache('pages');

        // Clear cache by page ID tag
        $pageCache->flushByTag('pageId_' . $templatePageUid);

        // Also clear the general hash cache which might contain related data
        $hashCache = $this->cacheManager->getCache('hash');
        $hashCache->flush();
    }
}
