<?php
namespace Bgs\LandingPages\DataProcessing;

use TYPO3\CMS\Frontend\ContentObject\ContentObjectRenderer;
use TYPO3\CMS\Frontend\ContentObject\DataProcessorInterface;
use TYPO3\CMS\Core\Utility\GeneralUtility;
use Bgs\LandingPages\Domain\Repository\FlightFromToRepository;

/**
 * DataProcessor for Flight From-To 2 content element
 * Processes simple lists of code-name pairs for from/to display
 */
class FlightFromToProcessor implements DataProcessorInterface
{
    /**
     * Process data for the Flight From-To 2 content element
     *
     * @param ContentObjectRenderer $cObj The content object renderer
     * @param array $contentObjectConfiguration The TypoScript configuration
     * @param array $processorConfiguration The processor configuration
     * @param array $processedData The processed data to be passed to the template
     * @return array The processed data with parsed from/to lists
     */
    public function process(
        ContentObjectRenderer $cObj,
        array $contentObjectConfiguration,
        array $processorConfiguration,
        array $processedData
    ): array {
        // Get FlightFromTo repository and find record by content element UID
        $flightFromToRepository = GeneralUtility::makeInstance(FlightFromToRepository::class);
        $contentElementUid = (int)($processedData['data']['uid'] ?? 0);

        $flightFromTo = $flightFromToRepository->findByContentElementUid($contentElementUid);

        // Get the raw from and to lists from the FlightFromTo record
        $fromListRaw = $flightFromTo ? $flightFromTo->getFromList() : '';
        $toListRaw = $flightFromTo ? $flightFromTo->getToList() : '';

        // Parse the from list
        $fromList = $this->parseCodeNameList($fromListRaw);

        // Parse the to list
        $toList = $this->parseCodeNameList($toListRaw);

        // Get additional fields from the FlightFromTo record
        $fromTitle = $flightFromTo ? $flightFromTo->getFromTitle() : '';
        $fromItem = $flightFromTo ? $flightFromTo->getFromItem() : '';
        $fromEndpoint = $flightFromTo ? $flightFromTo->getFromEndpoint() : '';
        $fromHref = $flightFromTo ? $flightFromTo->getFromHref() : '';
        $toTitle = $flightFromTo ? $flightFromTo->getToTitle() : '';
        $toItem = $flightFromTo ? $flightFromTo->getToItem() : '';
        $toEndpoint = $flightFromTo ? $flightFromTo->getToEndpoint() : '';
        $toHref = $flightFromTo ? $flightFromTo->getToHref() : '';

        // Process item-specific placeholders for from list
        $fromList = $this->processItemPlaceholders($fromList, $fromItem, $fromEndpoint, $fromHref);

        // Process item-specific placeholders for to list
        $toList = $this->processItemPlaceholders($toList, $toItem, $toEndpoint, $toHref);

        // Add parsed data to processed data array
        $processedData['fromList'] = $fromList;
        $processedData['toList'] = $toList;
        $processedData['hasFromList'] = !empty($fromList);
        $processedData['hasToList'] = !empty($toList);
        $processedData['hasAnyList'] = !empty($fromList) || !empty($toList);

        // Add new fields to processed data (these are now processed per-item)
        $processedData['fromTitle'] = $fromTitle;
        $processedData['fromItem'] = $fromItem;
        $processedData['fromEndpoint'] = $fromEndpoint;
        $processedData['fromHref'] = $fromHref;
        $processedData['toTitle'] = $toTitle;
        $processedData['toItem'] = $toItem;
        $processedData['toEndpoint'] = $toEndpoint;
        $processedData['toHref'] = $toHref;

        return $processedData;
    }

    /**
     * Parse a code-name list from textarea input
     * Expected format: "CODE - Name" per line
     *
     * @param string $listText Raw textarea content
     * @return array Array of parsed items with 'code' and 'name' keys
     */
    protected function parseCodeNameList(string $listText): array
    {
        $items = [];
        
        if (empty(trim($listText))) {
            return $items;
        }

        // Split by lines and process each line
        $lines = explode("\n", $listText);
        
        foreach ($lines as $line) {
            $line = trim($line);
            
            // Skip empty lines
            if (empty($line)) {
                continue;
            }

            // Look for the pattern "CODE - Name"
            if (strpos($line, ' - ') !== false) {
                $parts = explode(' - ', $line, 2);
                $code = trim($parts[0]);
                $name = trim($parts[1]);
                
                if (!empty($code) && !empty($name)) {
                    $items[] = [
                        'code' => $code,
                        'name' => $name,
                        'display' => $code . ' - ' . $name
                    ];
                }
            } else {
                // If no dash separator found, treat the whole line as code
                $items[] = [
                    'code' => $line,
                    'name' => '',
                    'display' => $line
                ];
            }
        }

        return $items;
    }

    /**
     * Process item-specific placeholders for each item in the list
     * Replaces {item.code} and {item.name} with actual values for each item
     *
     * @param array $items Array of parsed items with 'code' and 'name' keys
     * @param string $itemTemplate Template string for item display (may contain placeholders)
     * @param string $endpointTemplate Template string for endpoint URL (may contain placeholders)
     * @param string $hrefTemplate Template string for href URL (may contain placeholders)
     * @return array Array of items with processed placeholders
     */
    protected function processItemPlaceholders(array $items, string $itemTemplate, string $endpointTemplate, string $hrefTemplate): array
    {
        // If no templates are provided, return items as-is
        if (empty($itemTemplate) && empty($endpointTemplate) && empty($hrefTemplate)) {
            return $items;
        }

        foreach ($items as &$item) {
            // Process item template (for data-title attribute)
            if (!empty($itemTemplate)) {
                $item['processedItem'] = $this->replacePlaceholdersInTemplate($itemTemplate, $item);
            }

            // Process endpoint template (for data-endpoint attribute)
            if (!empty($endpointTemplate)) {
                $item['processedEndpoint'] = $this->replacePlaceholdersInTemplate($endpointTemplate, $item);
            }

            // Process href template (for href attribute)
            if (!empty($hrefTemplate)) {
                $item['processedHref'] = $this->replacePlaceholdersInTemplate($hrefTemplate, $item);
            }
        }

        return $items;
    }

    /**
     * Replace {item.code} and {item.name} placeholders in a template string
     *
     * @param string $template Template string with placeholders
     * @param array $item Item data with 'code' and 'name' keys
     * @return string Processed template with placeholders replaced
     */
    protected function replacePlaceholdersInTemplate(string $template, array $item): string
    {
        $processed = $template;

        // Replace {item.code} with actual code
        $processed = str_replace('{item.code}', $item['code'] ?? '', $processed);

        // Replace {item.name} with actual name
        $processed = str_replace('{item.name}', $item['name'] ?? '', $processed);

        return $processed;
    }
}
