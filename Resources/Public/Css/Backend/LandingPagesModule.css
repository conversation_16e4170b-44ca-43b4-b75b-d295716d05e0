/**
 * Backend Module Styles for Landing Pages Extension
 */

/* Module specific styles */
.landing-pages-module {
    padding: 20px;
}

/* Statistics cards */
.statistics-card {
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.statistics-card h4 {
    margin-top: 0;
    color: #333;
    font-weight: 600;
}

.statistics-number {
    font-size: 2.5em;
    font-weight: bold;
    color: #EF395D;
    line-height: 1;
}

.statistics-label {
    color: #666;
    font-size: 0.9em;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Module icon styling */
.module-icon {
    background-color: #EF395D;
    border-radius: 50%;
    width: 80px;
    height: 80px;
    margin: 0 auto;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 2em;
}

/* Flight routes table */
.flight-routes-table {
    margin-top: 20px;
}

.flight-routes-table th {
    background-color: #f8f9fa;
    font-weight: 600;
    border-bottom: 2px solid #dee2e6;
}

.flight-routes-table .badge {
    font-size: 0.8em;
}

.badge-active {
    background-color: #28a745;
}

.badge-inactive {
    background-color: #6c757d;
}

/* Action buttons */
.action-buttons {
    margin-bottom: 20px;
}

.action-buttons .btn {
    margin-right: 10px;
}

/* Alert styling */
.alert-landing-pages {
    border-left: 4px solid #EF395D;
    background-color: #f8f9fa;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .statistics-card {
        margin-bottom: 15px;
    }
    
    .statistics-number {
        font-size: 2em;
    }
    
    .module-icon {
        width: 60px;
        height: 60px;
        font-size: 1.5em;
    }
}

/* Loading states */
.loading-overlay {
    position: relative;
}

.loading-overlay::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.8);
    display: none;
}

.loading-overlay.loading::after {
    display: block;
}

/* Error states */
.error-message {
    color: #dc3545;
    font-style: italic;
    margin-top: 10px;
}

/* Success states */
.success-message {
    color: #28a745;
    font-weight: 500;
    margin-top: 10px;
}
