<html xmlns:f="http://typo3.org/ns/TYPO3/CMS/Fluid/ViewHelpers"
      xmlns:be="http://typo3.org/ns/TYPO3/CMS/Backend/ViewHelpers"
      data-namespace-typo3-fluid="true">

<f:layout name="Module" />

<f:section name="Content">

        <!-- Description -->
        <div class="alert alert-info">
            <f:translate key="LLL:EXT:landing-pages/Resources/Private/Language/locallang_mod.xlf:overview.description" />
        </div>

        <!-- Statistics Summary -->
        <div class="row">
            <div class="col-md-3">
                <div class="panel panel-default">
                    <div class="panel-body text-center">
                        <h3 class="text-primary">{statistics.totalRoutes}</h3>
                        <small><f:translate key="LLL:EXT:landing-pages/Resources/Private/Language/locallang_mod.xlf:statistics.total_routes" /></small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="panel panel-default">
                    <div class="panel-body text-center">
                        <h3 class="text-success">{statistics.activeRoutes}</h3>
                        <small><f:translate key="LLL:EXT:landing-pages/Resources/Private/Language/locallang_mod.xlf:statistics.active_routes" /></small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="panel panel-default">
                    <div class="panel-body text-center">
                        <h3 class="text-warning">{statistics.inactiveRoutes}</h3>
                        <small><f:translate key="LLL:EXT:landing-pages/Resources/Private/Language/locallang_mod.xlf:statistics.inactive_routes" /></small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="panel panel-default">
                    <div class="panel-body text-center">
                        <h3 class="text-info">{extensionVersion}</h3>
                        <small><f:translate key="LLL:EXT:landing-pages/Resources/Private/Language/locallang_mod.xlf:extension.version" /></small>
                    </div>
                </div>
            </div>
        </div>

        <!-- Flight Routes Table -->
        <div class="panel panel-default">
            <div class="panel-heading">
                <h3 class="panel-title">Flight Routes</h3>
            </div>
            <div class="panel-body">
                <f:if condition="{flightRoutes}">
                    <f:then>
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead>
                                    <tr>
                                        <th>Origin</th>
                                        <th>Destination</th>
                                        <th>Slug</th>
                                        <th>Status</th>
                                        <th>Landing Page</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <f:for each="{flightRoutes}" as="route">
                                        <tr>
                                            <td>
                                                <strong>{route.originName}</strong><br>
                                                <small class="text-muted">{route.originCode}</small>
                                            </td>
                                            <td>
                                                <strong>{route.destinationName}</strong><br>
                                                <small class="text-muted">{route.destinationCode}</small>
                                            </td>
                                            <td>
                                                <code>{route.slug}</code>
                                            </td>
                                            <td>
                                                <f:if condition="{route.active}">
                                                    <f:then>
                                                        <span class="label label-success">Active</span>
                                                    </f:then>
                                                    <f:else>
                                                        <span class="label label-default">Inactive</span>
                                                    </f:else>
                                                </f:if>
                                            </td>
                                            <td>
                                                <f:if condition="{route.landingPage}">
                                                    <f:then>
                                                        {route.landingPage.title}
                                                    </f:then>
                                                    <f:else>
                                                        <span class="text-muted">No landing page</span>
                                                    </f:else>
                                                </f:if>
                                            </td>
                                        </tr>
                                    </f:for>
                                </tbody>
                            </table>
                        </div>
                    </f:then>
                    <f:else>
                        <div class="alert alert-warning">
                            <f:translate key="LLL:EXT:landing-pages/Resources/Private/Language/locallang_mod.xlf:overview.no_routes" />
                        </div>
                    </f:else>
                </f:if>
            </div>
        </div>
    </div>
</f:section>

</html>
