<html xmlns:f="http://typo3.org/ns/TYPO3/CMS/Fluid/ViewHelpers" data-namespace-typo3-fluid="true">

<f:if condition="{data.header}">
    <f:render section="Header" arguments="{_all}" />
</f:if>

<f:if condition="{flightRoutes}">
    <f:then>
        <div class="flight-destinations-menu">
            <ul class="destinations-list">
                <f:for each="{flightRoutes}" as="item">
                    <f:if condition="{item.type} == 'flight_route'">
                        <f:then>
                            <li class="destination-item flight-route-item">
                                <a href="{item.fullUrl}" class="destination-link">
                                    <span class="origin">{item.originName}</span>
                                    <span class="separator">→</span>
                                    <span class="destination">{item.destinationName}</span>
                                </a>
                            </li>
                        </f:then>
                        <f:else>
                            <li class="destination-item child-page-item">
                                <a href="{item.fullUrl}" class="destination-link">
                                    <span class="page-title">{item.title}</span>
                                </a>
                            </li>
                        </f:else>
                    </f:if>
                </f:for>
            </ul>
        </div>
    </f:then>
    <f:else>
        <div class="flight-destinations-menu-empty">
            <p>No destinations available.</p>
            <f:if condition="{storagePid}">
                <p><small>Storage Page: {storagePid}</small></p>
            </f:if>
        </div>
    </f:else>
</f:if>

<f:section name="Header">
    <f:switch expression="{data.header_layout}">
        <f:case value="1"><h1{f:if(condition: data.header_class, then: ' class="{data.header_class}"')}>{data.header}</h1></f:case>
        <f:case value="2"><h2{f:if(condition: data.header_class, then: ' class="{data.header_class}"')}>{data.header}</h2></f:case>
        <f:case value="3"><h3{f:if(condition: data.header_class, then: ' class="{data.header_class}"')}>{data.header}</h3></f:case>
        <f:case value="4"><h4{f:if(condition: data.header_class, then: ' class="{data.header_class}"')}>{data.header}</h4></f:case>
        <f:case value="5"><h5{f:if(condition: data.header_class, then: ' class="{data.header_class}"')}>{data.header}</h5></f:case>
        <f:case value="6"><h6{f:if(condition: data.header_class, then: ' class="{data.header_class}"')}>{data.header}</h6></f:case>
        <f:defaultCase><h2{f:if(condition: data.header_class, then: ' class="{data.header_class}"')}>{data.header}</h2></f:defaultCase>
    </f:switch>
</f:section>

</html>
