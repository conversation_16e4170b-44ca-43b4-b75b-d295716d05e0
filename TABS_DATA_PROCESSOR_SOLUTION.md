# Solution for the lib.tab1 Problem

## Problem Statement

The landing-pages extension was previously defining global `lib.tab1`, `lib.tab2`, and `lib.travelpoint` objects in its TypoScript setup. This approach is not sustainable because:

1. The extension cannot know all the lib objects that might be used across different sites.
2. Global definitions can interfere with site-specific TypoScript.
3. It creates a tight coupling between the extension and the templates that use these objects.

## Solution

After examining the felogin extension's approach to handling similar situations, I've implemented a solution that:

1. Creates a data processor (`TabsDataProcessor`) that provides default values for tab1, tab2, and travelpoint.
2. Configures this data processor in the TypoScript setup for landing pages.
3. Modifies the template to use the data provided by the data processor instead of directly accessing the lib objects.

### Benefits of this Approach

1. **Decoupling**: The extension no longer needs to define global lib objects. Instead, it provides a data processor that can be used by templates that need these values.
2. **Fallback Values**: The data processor provides default values for tab1, tab2, and travelpoint, ensuring that templates still work even if these objects aren't defined in the site's TypoScript.
3. **Flexibility**: Site administrators can still override these values by defining their own lib.tab1, lib.tab2, and lib.travelpoint objects in their site's TypoScript.
4. **Consistency**: This approach is consistent with TYPO3's best practices for extension development, as demonstrated by the felogin extension.

### Implementation Details

1. Created a new data processor class: `Bgs\LandingPages\DataProcessing\TabsDataProcessor`
2. Added this data processor to the TypoScript configuration for landing pages:
   ```typoscript
   page.10.dataProcessing {
       1010 = Bgs\LandingPages\DataProcessing\TabsDataProcessor
       1010 {
           as = tabsData
       }
   }
   ```
3. Modified the template to use the data provided by the data processor:
   ```html
   <div class="form-tab form-tab_packets" data-targetcontent="#form-content-packets">{tabsData.tab1}</div>
   <f:if condition="{tabsData.tab2} !== ''">
       <div class="form-tab form-tab_hotels" data-targetcontent="#form-content-hotels">{tabsData.tab2}</div>
   </f:if>
   ```

## Conclusion

This solution provides a more sustainable and flexible approach to handling the lib.tab1 problem. It follows TYPO3's best practices for extension development and ensures that templates still work even if these objects aren't defined in the site's TypoScript.
