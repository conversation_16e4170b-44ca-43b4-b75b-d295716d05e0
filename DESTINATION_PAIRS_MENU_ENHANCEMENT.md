# Destination Pairs Menu Enhancement

## Overview

The Destination Pairs Menu content element has been enhanced to display both flight destination pairs and child pages of the landing page in a unified menu. This allows for adding special case pages normally while displaying them alongside flight routes.

## Changes Made

### 1. Enhanced DestinationPairsMenuProcessor

**File**: `Classes/DataProcessing/DestinationPairsMenuProcessor.php`

**New Features**:
- Added support for fetching child pages of the current page
- Combined flight routes and child pages into a unified menu structure
- Added type indicators to distinguish between flight routes and regular pages
- Implemented proper URL generation for child pages
- Added sorting logic (flight routes first, then child pages, alphabetically within each type)

**New Methods**:
- `getChildPages(int $pageId)`: Fetches visible child pages of the current page
- `generatePageUrl(int $pageId, $site = null)`: Generates proper URLs for child pages

### 2. Updated Template

**File**: `Resources/Private/Templates/ContentElements/DestinationPairsMenu.html`

**Changes**:
- Modified to handle both flight routes and child pages
- Added conditional rendering based on item type
- Maintained the arrow format (→) for flight routes
- Used simple page titles for child pages
- Added CSS classes for visual distinction

### 3. Enhanced CSS Styling

**File**: `Resources/Public/CSS/styles.css`

**New Styles**:
- `.flight-route-item`: Blue gradient background for flight routes
- `.child-page-item`: Light gray background for child pages
- Added page icon (📄) for child pages
- Hover effects and transitions
- Responsive design considerations

## Data Structure

The enhanced processor now provides menu items with the following structure:

```php
[
    'type' => 'flight_route' | 'child_page',
    'title' => 'Display title',
    'fullUrl' => 'Generated URL',
    
    // For flight routes:
    'originCode' => 'Origin airport code',
    'originName' => 'Origin name',
    'destinationCode' => 'Destination airport code',
    'destinationName' => 'Destination name',
    'routeSlug' => 'Route slug',
    
    // For child pages:
    'uid' => 'Page UID',
    'doktype' => 'Page doktype'
]
```

## Usage

The content element works exactly as before but now automatically includes child pages:

1. **Flight Routes**: Displayed with origin → destination format in blue styling
2. **Child Pages**: Displayed with page title and page icon in gray styling
3. **Sorting**: Flight routes appear first, followed by child pages, both sorted alphabetically

## Configuration

No additional configuration is required. The enhancement is backward compatible and automatically includes child pages when they exist.

### Child Page Criteria

Child pages are included if they meet these criteria:
- Direct children of the current page (pid = current page uid)
- Not deleted (`deleted = 0`)
- Not hidden (`hidden = 0`)
- Not hidden from navigation (`nav_hide = 0`)
- Ordered by their sorting value

## Benefits

1. **Unified Menu**: Both flight routes and special pages in one menu
2. **Visual Distinction**: Clear visual differences between route types
3. **Flexible Content**: Allows adding custom pages alongside automated routes
4. **Backward Compatible**: Existing installations continue to work unchanged
5. **SEO Friendly**: Proper URL generation for all menu items

## Example Use Cases

- Adding "Special Offers" pages alongside flight routes
- Including "Travel Information" pages for specific destinations
- Adding seasonal or promotional pages
- Creating custom landing pages for specific campaigns

## Technical Notes

- Uses TYPO3's ConnectionPool for database queries
- Leverages site router for proper URL generation
- Respects TYPO3's page visibility settings
- Maintains proper sorting and ordering
- Includes error handling for URL generation
