<?php
namespace Bgs\LandingPages\Tests\Unit\Hooks;

use Bgs\LandingPages\Hooks\DataHandlerHook;
use Bgs\LandingPages\Service\SlugUpdateService;
use Bgs\LandingPages\Service\TemplateResolutionService;
use PHPUnit\Framework\TestCase;
use TYPO3\CMS\Core\Cache\CacheManager;
use TYPO3\CMS\Core\Cache\Frontend\FrontendInterface;
use TYPO3\CMS\Core\Database\ConnectionPool;
use TYPO3\CMS\Core\Database\Query\QueryBuilder;
use TYPO3\CMS\Core\Database\Query\Expression\ExpressionBuilder;
use TYPO3\CMS\Core\DataHandling\DataHandler;
use TYPO3\CMS\Core\Utility\GeneralUtility;
use TYPO3\TestingFramework\Core\Unit\UnitTestCase;

/**
 * Test case for DataHandlerHook
 */
class DataHandlerHookTest extends UnitTestCase
{
    protected DataHandlerHook $dataHandlerHook;
    protected SlugUpdateService $slugUpdateServiceMock;
    protected TemplateResolutionService $templateResolutionServiceMock;
    protected CacheManager $cacheManagerMock;
    protected ConnectionPool $connectionPoolMock;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create mocks
        $this->slugUpdateServiceMock = $this->createMock(SlugUpdateService::class);
        $this->templateResolutionServiceMock = $this->createMock(TemplateResolutionService::class);
        $this->cacheManagerMock = $this->createMock(CacheManager::class);
        $this->connectionPoolMock = $this->createMock(ConnectionPool::class);

        // Mock GeneralUtility::makeInstance calls
        GeneralUtility::addInstance(SlugUpdateService::class, $this->slugUpdateServiceMock);
        GeneralUtility::addInstance(TemplateResolutionService::class, $this->templateResolutionServiceMock);
        GeneralUtility::addInstance(CacheManager::class, $this->cacheManagerMock);
        GeneralUtility::addInstance(ConnectionPool::class, $this->connectionPoolMock);

        $this->dataHandlerHook = new DataHandlerHook();
    }

    protected function tearDown(): void
    {
        GeneralUtility::purgeInstances();
        parent::tearDown();
    }

    /**
     * @test
     */
    public function processDatamapAfterDatabaseOperationsHandlesFlightRouteUpdate(): void
    {
        // Arrange
        $routeId = 123;
        $fieldArray = [
            'pid' => 456,
            'origin_type' => 'airport',
            'destination_type' => 'country'
        ];
        $templatePageUid = 789;

        // Mock template resolution
        $this->templateResolutionServiceMock
            ->expects(self::once())
            ->method('resolveTemplatePageForRoute')
            ->with(456, 'airport', 'country')
            ->willReturn($templatePageUid);

        // Mock cache clearing
        $pageCacheMock = $this->createMock(FrontendInterface::class);
        $hashCacheMock = $this->createMock(FrontendInterface::class);

        $this->cacheManagerMock
            ->expects(self::exactly(2))
            ->method('getCache')
            ->withConsecutive(['pages'], ['hash'])
            ->willReturnOnConsecutiveCalls($pageCacheMock, $hashCacheMock);

        $pageCacheMock
            ->expects(self::once())
            ->method('flushByTag')
            ->with('pageId_' . $templatePageUid);

        $hashCacheMock
            ->expects(self::once())
            ->method('flush');

        // Mock DataHandler
        $dataHandlerMock = $this->createMock(DataHandler::class);

        // Act
        $this->dataHandlerHook->processDatamap_afterDatabaseOperations(
            'new',
            'tx_landingpages_domain_model_flightroute',
            $routeId,
            $fieldArray,
            $dataHandlerMock
        );

        // Assert - expectations are verified by PHPUnit
    }

    /**
     * @test
     */
    public function processDatamapAfterDatabaseOperationsIgnoresNonFlightRouteTables(): void
    {
        // Arrange
        $this->templateResolutionServiceMock
            ->expects(self::never())
            ->method('resolveTemplatePageForRoute');

        $this->cacheManagerMock
            ->expects(self::never())
            ->method('getCache');

        $dataHandlerMock = $this->createMock(DataHandler::class);

        // Act
        $this->dataHandlerHook->processDatamap_afterDatabaseOperations(
            'update',
            'pages',
            123,
            ['title' => 'Test Page'],
            $dataHandlerMock
        );

        // Assert - expectations are verified by PHPUnit
    }

    /**
     * @test
     */
    public function processDatamapAfterDatabaseOperationsHandlesInvalidRouteData(): void
    {
        // Arrange
        $routeId = 123;
        $fieldArray = [
            'pid' => 0, // Invalid landing page UID
            'origin_type' => 'airport',
            'destination_type' => 'country'
        ];

        $this->templateResolutionServiceMock
            ->expects(self::never())
            ->method('resolveTemplatePageForRoute');

        $this->cacheManagerMock
            ->expects(self::never())
            ->method('getCache');

        $dataHandlerMock = $this->createMock(DataHandler::class);

        // Act
        $this->dataHandlerHook->processDatamap_afterDatabaseOperations(
            'new',
            'tx_landingpages_domain_model_flightroute',
            $routeId,
            $fieldArray,
            $dataHandlerMock
        );

        // Assert - expectations are verified by PHPUnit
    }

    /**
     * @test
     */
    public function processDatamapAfterDatabaseOperationsHandlesZeroTemplatePageUid(): void
    {
        // Arrange
        $routeId = 123;
        $fieldArray = [
            'pid' => 456,
            'origin_type' => 'airport',
            'destination_type' => 'country'
        ];

        // Mock template resolution returning 0 (no template found)
        $this->templateResolutionServiceMock
            ->expects(self::once())
            ->method('resolveTemplatePageForRoute')
            ->with(456, 'airport', 'country')
            ->willReturn(0);

        // Cache should not be cleared when no template is found
        $this->cacheManagerMock
            ->expects(self::never())
            ->method('getCache');

        $dataHandlerMock = $this->createMock(DataHandler::class);

        // Act
        $this->dataHandlerHook->processDatamap_afterDatabaseOperations(
            'new',
            'tx_landingpages_domain_model_flightroute',
            $routeId,
            $fieldArray,
            $dataHandlerMock
        );

        // Assert - expectations are verified by PHPUnit
    }

    /**
     * Helper method to call protected methods
     */
    protected function callProtectedMethod($object, $methodName, array $parameters = [])
    {
        $reflection = new \ReflectionClass(get_class($object));
        $method = $reflection->getMethod($methodName);
        $method->setAccessible(true);

        return $method->invokeArgs($object, $parameters);
    }
}
