# Fluid Templates in TYPO3 v12 Backend Modules

## Overview

This document provides comprehensive guidance on implementing Fluid templates in TYPO3 v12.04 backend modules based on official documentation and best practices.

## Key Changes in TYPO3 v12

### **1. ModuleTemplate API Simplification**
TYPO3 v12 introduced a simplified ModuleTemplate API that streamlines backend module development:

- **New**: `ModuleTemplateFactory::create()` - Primary method for creating module templates
- **New**: `renderResponse()` - Preferred rendering method
- **Deprecated**: `setContent()`, `renderContent()`, `getView()` - Will be removed in v13

### **2. Template Structure Requirements**
Backend module templates must use the Module layout:

```html
<f:layout name="Module" />

<f:section name="Content">
    <!-- Your module content here -->
</f:section>
```

## Recommended Implementation Pattern

### **Controller Structure**

```php
<?php

declare(strict_types=1);

namespace Vendor\Extension\Controller\Backend;

use Psr\Http\Message\ResponseInterface;
use Psr\Http\Message\ServerRequestInterface;
use TYPO3\CMS\Backend\Attribute\AsController;
use TYPO3\CMS\Backend\Template\ModuleTemplateFactory;

#[AsController]
class MyModuleController
{
    public function __construct(
        private readonly ModuleTemplateFactory $moduleTemplateFactory,
        // ... other dependencies
    ) {}

    public function indexAction(ServerRequestInterface $request): ResponseInterface
    {
        // HTTP method validation
        if ($request->getMethod() !== 'GET') {
            throw new MethodNotAllowedException('Method not allowed', 405);
        }

        $moduleTemplate = $this->moduleTemplateFactory->create($request);
        $moduleTemplate->setTitle('My Module');

        // Add CSS for styling
        $this->pageRenderer->addCssFile('EXT:my_extension/Resources/Public/Css/Backend/Module.css');

        // Add navigation buttons
        $this->addNavigationButtons($moduleTemplate, 'index');

        // Assign variables to template
        $moduleTemplate->assignMultiple([
            'data' => $this->getData(),
            'version' => $this->getExtensionVersion(),
        ]);

        return $moduleTemplate->renderResponse('Backend/MyModule/Index');
    }
}
```

### **Template Directory Structure**

```
Resources/
└── Private/
    └── Templates/
        └── Backend/
            └── MyModule/
                ├── Index.html
                └── Overview.html
```

### **Template Example**

```html
<html xmlns:f="http://typo3.org/ns/TYPO3/CMS/Fluid/ViewHelpers"
      xmlns:be="http://typo3.org/ns/TYPO3/CMS/Backend/ViewHelpers"
      data-namespace-typo3-fluid="true">

<f:layout name="Module" />

<f:section name="Content">
    <div class="my-module">
        <h1>My Module Dashboard</h1>
        
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-body">
                        <h5 class="card-title">Statistics</h5>
                        <p class="card-text">Total items: {data.totalItems}</p>
                    </div>
                </div>
            </div>
        </div>
        
        <f:if condition="{data.error}">
            <div class="alert alert-danger">
                <strong>Error:</strong> {data.error}
            </div>
        </f:if>
    </div>
</f:section>

</html>
```

## Best Practices

### **1. Security**
- Always validate HTTP methods
- Use proper exception handling
- Sanitize user inputs

### **2. Error Handling**
- Implement try-catch blocks for data operations
- Display user-friendly error messages
- Log errors appropriately

### **3. Styling**
- Use dedicated CSS files for backend modules
- Follow TYPO3 backend design patterns
- Implement responsive design

### **4. Navigation**
- Use DocHeader buttons for navigation
- Implement proper button states (active/inactive)
- Use appropriate icons

## Common Patterns

### **Adding DocHeader Buttons**

```php
private function addNavigationButtons($moduleTemplate, string $currentAction): void
{
    $buttonBar = $moduleTemplate->getDocHeaderComponent()->getButtonBar();

    $dashboardButton = $buttonBar->makeLinkButton()
        ->setTitle('Dashboard')
        ->setIcon($this->iconFactory->getIcon('actions-home', 'small'))
        ->setHref($this->uriBuilder->buildUriFromRoute('my_module'));

    if ($currentAction === 'index') {
        $dashboardButton->setClasses('active');
    }

    $buttonBar->addButton($dashboardButton, ButtonBar::BUTTON_POSITION_LEFT);
}
```

### **Flash Messages**

```php
$moduleTemplate->addFlashMessage(
    'Operation completed successfully',
    'Success',
    ContextualFeedbackSeverity::OK
);
```

### **Template Path Convention**
- Use `Backend/ControllerName/ActionName` pattern
- Example: `Backend/LandingPagesModule/Index`
- Templates go in `Resources/Private/Templates/Backend/`

## Migration from Previous Versions

### **From TYPO3 v11 to v12**

**Old approach (v11):**
```php
$view = $this->view;
$view->assign('data', $data);
return $this->htmlResponse($view->render());
```

**New approach (v12):**
```php
$moduleTemplate = $this->moduleTemplateFactory->create($request);
$moduleTemplate->assignMultiple(['data' => $data]);
return $moduleTemplate->renderResponse('Backend/Module/Action');
```

## Troubleshooting

### **Common Issues**

1. **Template not found errors**
   - Verify template path follows convention
   - Check file permissions
   - Ensure proper directory structure

2. **Layout errors**
   - Always use `<f:layout name="Module" />`
   - Wrap content in `<f:section name="Content">`

3. **Styling issues**
   - Include CSS files properly
   - Use TYPO3 backend CSS classes
   - Test responsive behavior

### **Debugging**
- Check TYPO3 logs: `ddev logs | grep -i error`
- Verify cache is cleared: `ddev typo3 cache:flush`
- Use browser developer tools for frontend issues

## Performance Considerations

1. **Template Caching**
   - TYPO3 automatically caches compiled templates
   - Clear cache when making template changes

2. **Asset Loading**
   - Load CSS/JS files efficiently
   - Use TYPO3's asset management

3. **Data Fetching**
   - Implement proper error handling
   - Use caching for expensive operations

## Conclusion

The TYPO3 v12 ModuleTemplate API provides a clean, standardized approach to backend module development. By following these patterns and best practices, you can create maintainable, secure, and user-friendly backend modules that integrate seamlessly with the TYPO3 backend.
