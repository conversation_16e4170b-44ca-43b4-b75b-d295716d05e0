# Flight Landing Pages Extension - Product Requirements Document

## Overview

The Flight Landing Pages extension provides a system for creating dynamic flight route landing pages in TYPO3. The extension enables content managers to create template pages with flight-specific placeholders that generate multiple virtual landing pages for different flight routes, all while maintaining standard TYPO3 page rendering and site integration.

## Core Concepts

### Template Pages
- Special page type that serves as a content template
- Contains regular TYPO3 content elements with flight route placeholders
- Not directly accessible via frontend URLs
- Defines the layout, content structure, and SEO metadata for generated landing pages

### Landing Pages
- Configuration pages that define URL patterns and reference template pages
- Manage collections of flight route records
- Generate virtual routes based on associated flight route data
- Render normally as standard TYPO3 pages when accessed directly

### Flight Route Records
- Define origin and destination pairs (airports, cities, or countries)
- Generate SEO-friendly URL slugs
- Contain metadata for placeholder replacement
- Can be activated/deactivated individually

## Functional Requirements

### Backend Interface

#### Landing Page Management
- Create and manage landing pages using standard TYPO3 page interface with additional fields
- Configure template page references
- Landing page URL serves as prefix for all flight route URLs
- Manage caching and sitemap settings
- Access control follows standard TYPO3 page permissions

#### Flight Route Records Management
- List view of all route records for a landing page
- Create, edit, and delete individual route records
- Bulk operations for route management
- Search and filter functionality
- Activation/deactivation controls

#### Import/Export Functionality
- CSV export of flight route records
- CSV import with duplicate detection
- Validation of imported data
- Error reporting for failed imports
- Support for bulk route creation

#### Template Page Interface
- Standard TYPO3 page editing interface
- Preview functionality for route-specific rendering
- Placeholder legend and documentation
- Content element management with placeholder support

#### Advanced Backend Preview
- Comprehensive preview interface in TYPO3 page layout view
- Template page information display with validation status and warnings
- Complete flight route listing with search and filtering capabilities
- Real-time route statistics (total, active, inactive counts)
- Individual route preview and edit buttons with TYPO3 native styling
- Bulk operations through CSV export and import functionality
- Quick route creation with pre-filled landing page references
- Live search functionality with instant filtering and results count
- Integration with TYPO3's standard button groups and design patterns
- Template page validation with visual status indicators
- Route status badges for active/inactive routes
- Responsive design that works on all screen sizes
- Keyboard shortcuts for search (Escape to clear)
- Direct edit links that maintain backend context
- Preview URLs that open in new tabs to preserve backend workflow

### Frontend Rendering

#### Standard Page Rendering
- Virtual routes render as normal TYPO3 pages
- Use site's existing PAGE object configuration
- Maintain site templates, layouts, and partials
- Preserve navigation, styling, and functionality
- Support all TYPO3 features (caching, redirects, etc.)

#### Placeholder Processing
- Replace flight route placeholders in template content
- Support placeholders in content elements, SEO metadata, and page properties
- Use bracket-underscore format: `[_placeholder_]`
- Available placeholders: origin_code, origin_name, origin_type, destination_code, destination_name, destination_type
- Extended placeholder set including route_slug, is_active status
- Convenience placeholders: route (origin → destination), route_dash (origin-destination), route_text (origin to destination)
- Comprehensive field processing for headers, subheaders, bodytext, and links
- SEO field processing for titles, descriptions, and meta tags
- Dedicated PlaceholderService for consistent processing across all contexts
- Debug logging and validation for placeholder resolution

#### SEO and Metadata
- Copy SEO metadata from template page
- Process placeholders in title tags, meta descriptions, and structured data
- Generate dynamic page titles and descriptions
- Support for search engine optimization

### Multi-Site and Multi-Language Support

#### Site Integration
- Work seamlessly across multiple TYPO3 sites
- Respect site-specific configurations and templates
- Support different URL structures per site
- Maintain site-specific caching strategies

#### Language Support
- Full integration with TYPO3 language handling
- Support for translated template pages
- Localized flight route records
- Language-specific URL generation

### URL and Routing

#### URL Generation
- Automatic generation of SEO-friendly URLs
- Format: `{origin_code}-{destination_code}` (e.g., "ber-sof")
- URLs serve as SEO slugs for user and search engine benefit
- No functional data extraction from URLs required

#### Route Resolution
- Detect virtual route patterns in incoming requests
- Match URLs to corresponding flight route records
- Load appropriate template page content
- Handle 404 errors for non-existent routes

#### Advanced URL Generation
- Dedicated URL generation service for consistent URL building
- Site-aware URL generation that adapts to different TYPO3 sites
- Automatic URL parsing and component extraction
- Support for complex site structures with subdirectories
- Landing page slug integration as URL prefix
- Route slug generation with automatic conflict resolution
- Full URL building from individual components
- URL validation and error handling

### Caching Strategy

#### Template-Based Caching
- Virtual routes inherit caching settings from template pages
- Support for TYPO3's standard caching mechanisms
- Configurable cache lifetime per landing page
- Cache invalidation when template or route data changes

#### Performance Optimization
- Efficient route matching algorithms
- Minimal memory usage for large route collections
- Optimized database queries for route resolution
- Support for TYPO3's caching layers

### Content Management

#### Template Content Creation
- Standard TYPO3 content element creation
- Placeholder insertion in text and media elements
- Rich text editor support for placeholders
- Content element preview with placeholder examples

#### Content Element Plugin
- Flight Reference plugin for displaying route lists on any page
- Configurable display modes (list, grid, filtered views)
- Origin and destination filter functionality
- Site-aware route display (shows only routes for current site)
- Integration with TYPO3's content element system
- FlexForm configuration for plugin settings

#### Advanced Data Processing
- Multiple specialized data processors for different rendering contexts
- FlightRouteProcessor for landing page and virtual route data integration
- VirtualRouteDataProcessor for template variable management
- Comprehensive ViewHelper library for template flexibility
- ReplacePlaceholderViewHelper for manual placeholder processing
- FlightContentViewHelper for advanced content rendering
- VirtualRouteContentViewHelper for column-specific template content
- Automatic detection and handling of virtual route contexts
- Seamless integration with TYPO3's data processing pipeline

#### Route Preview
- Preview button to open individual routes in frontend
- Direct links to view how template content appears with specific route data
- Validate placeholder replacement in live environment
- Test URL generation and routing functionality

### Integration Requirements

#### Third-Party Integration
- Integration with Wizatour software system
- API connectivity for external flight data
- Data synchronization capabilities
- External system compatibility

#### TYPO3 Integration
- Full compatibility with TYPO3 v12.4+
- Standard extension installation procedures
- Integration with TYPO3's page tree and backend
- Support for TYPO3's security and access control systems
- Content element plugin for displaying flight route lists and filters

## Technical Constraints

### Page Types
- Template pages use doktype 200 (not directly accessible)
- Landing pages use doktype 201 (configuration pages)
- Virtual routes render as standard pages with dynamic content

### Data Storage
- Flight route records stored in dedicated database tables
- Template and landing page data extend standard pages table
- Maintain referential integrity between related records

### Performance
- No specific performance requirements beyond standard optimization
- Preference for faster execution and lower memory usage
- Support for high-traffic scenarios without special requirements

### Compatibility
- TYPO3 v12.4+ compatibility required
- Standard TYPO3 extension architecture
- No custom rendering engines or template systems
- Full integration with existing TYPO3 workflows

## Success Criteria

### Functional Success
- Content managers can create and manage flight landing pages without technical knowledge
- Virtual routes render identically to standard TYPO3 pages
- SEO metadata and placeholders process correctly
- Import/export functionality handles large datasets efficiently

### Technical Success
- Extension installs and operates without manual database modifications
- Performance remains acceptable with thousands of route records
- Multi-site and multi-language functionality works seamlessly
- Integration with Wizatour system operates reliably

### User Experience Success
- Backend interface follows TYPO3 design patterns and conventions
- Content editing workflow feels natural to TYPO3 users
- Preview functionality provides accurate representation of frontend output
- Error handling and validation provide clear feedback to users
