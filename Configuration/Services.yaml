services:
  _defaults:
    autowire: true
    autoconfigure: true
    public: false

  Bgs\LandingPages\:
    resource: '../Classes/*'
    exclude:
      - '../Classes/Domain/Model/*'
      - '../Classes/Routing/*'

  # Virtual Route Service
  Bgs\LandingPages\Service\VirtualRouteService:
    public: false
    arguments:
      $templateResolutionService: '@Bgs\LandingPages\Service\TemplateResolutionService'

  # Virtual Route Middleware (runs before PageResolver)
  Bgs\LandingPages\Middleware\VirtualRouteHandler:
    public: false
    arguments:
      $virtualRouteService: '@Bgs\LandingPages\Service\VirtualRouteService'

  # Dynamic Arguments Middleware (runs after PageArgumentValidator but before TypoScriptFrontendInitialization)
  Bgs\LandingPages\Middleware\DynamicArgumentsMiddleware:
    public: false

  # Virtual Route Event Listeners
  Bgs\LandingPages\EventListener\VirtualRouteDetectionListener:
    public: false
    arguments:
      $virtualRouteService: '@Bgs\LandingPages\Service\VirtualRouteService'
    tags:
      - name: event.listener
        identifier: 'landing-pages-virtual-route-detection'
        event: TYPO3\CMS\Frontend\Event\BeforePageIsResolvedEvent

  Bgs\LandingPages\EventListener\VirtualPageReplacementListener:
    public: false
    arguments:
      $virtualRouteService: '@Bgs\LandingPages\Service\VirtualRouteService'
    tags:
      - name: event.listener
        identifier: 'landing-pages-virtual-page-replacement'
        event: TYPO3\CMS\Frontend\Event\AfterPageWithRootLineIsResolvedEvent

  Bgs\LandingPages\EventListener\VirtualContentProcessingListener:
    public: false
    arguments:
      $virtualRouteService: '@Bgs\LandingPages\Service\VirtualRouteService'
    tags:
      - name: event.listener
        identifier: 'landing-pages-virtual-content-processing'
        event: TYPO3\CMS\Frontend\Event\AfterCacheableContentIsGeneratedEvent

  # Explicitly configure services that need special handling

  Bgs\LandingPages\Service\SiteConfigurationService:
    arguments:
      $siteFinder: '@TYPO3\CMS\Core\Site\SiteFinder'

  # Repository configuration - repositories are auto-configured by default
  Bgs\LandingPages\Domain\Repository\FlightRouteRepository:
    public: true

  # Template Resolution Service
  Bgs\LandingPages\Service\TemplateResolutionService:
    public: false

  # Controller configuration
  Bgs\LandingPages\Controller\DestinationsMenuController:
    public: true

  Bgs\LandingPages\Controller\FlightFromToController:
    public: true
    arguments:
      $virtualRouteService: '@Bgs\LandingPages\Service\VirtualRouteService'

  # Backend Module Controller
  Bgs\LandingPages\Controller\Backend\LandingPagesModuleController:
    public: true
    arguments:
      $moduleTemplateFactory: '@TYPO3\CMS\Backend\Template\ModuleTemplateFactory'
      $flightRouteRepository: '@Bgs\LandingPages\Domain\Repository\FlightRouteRepository'
      $virtualRouteService: '@Bgs\LandingPages\Service\VirtualRouteService'
      $pageRenderer: '@TYPO3\CMS\Core\Page\PageRenderer'

  # URL Generation Service
  Bgs\LandingPages\Service\UrlGenerationService:
    public: false
    arguments:
      $siteFinder: '@TYPO3\CMS\Core\Site\SiteFinder'

  # Backend preview event listener
  Bgs\LandingPages\EventListener\PagePreviewEventListener:
    public: false
    arguments:
      $flightRouteRepository: '@Bgs\LandingPages\Domain\Repository\FlightRouteRepository'
      $siteFinder: '@TYPO3\CMS\Core\Site\SiteFinder'
      $urlGenerationService: '@Bgs\LandingPages\Service\UrlGenerationService'
    tags:
      - name: event.listener
        identifier: 'landing-pages-preview'
        event: TYPO3\CMS\Backend\Controller\Event\ModifyPageLayoutContentEvent

  # Backend preview URI modification for flight routes
  Bgs\LandingPages\EventListener\ModifyFlightRoutePreviewUriEventListener:
    public: false
    arguments:
      $urlGenerationService: '@Bgs\LandingPages\Service\UrlGenerationService'
      $siteFinder: '@TYPO3\CMS\Core\Site\SiteFinder'
    tags:
      - name: event.listener
        identifier: 'landing-pages-modify-flight-route-preview-uri'
        event: TYPO3\CMS\Backend\Routing\Event\BeforePagePreviewUriGeneratedEvent

  # Backend CSV export controller
  Bgs\LandingPages\Controller\Backend\CsvExportController:
    public: true
    arguments:
      $urlGenerationService: '@Bgs\LandingPages\Service\UrlGenerationService'
      $templateResolutionService: '@Bgs\LandingPages\Service\TemplateResolutionService'
      $cacheManager: '@TYPO3\CMS\Core\Cache\CacheManager'

  # Console commands
  Bgs\LandingPages\Command\UpdateSlugCommand:
    tags:
      - name: 'console.command'
        command: 'landing:update-slugs'

  Bgs\LandingPages\Command\UpdateChildSlugsCommand:
    arguments:
      $slugUpdateService: '@Bgs\LandingPages\Service\SlugUpdateService'
    tags:
      - name: 'console.command'
        command: 'landing:update-child-slugs'

  # Slug Update Service
  Bgs\LandingPages\Service\SlugUpdateService:
    public: false
