<?php

declare(strict_types=1);

use TYPO3\CMS\Core\Imaging\IconProvider\SvgIconProvider;

return [
    // Backend module icon
    'module-landing-pages' => [
        'provider' => SvgIconProvider::class,
        'source' => 'EXT:landing-pages/Resources/Public/Icons/module-landing-pages.svg',
    ],
    
    // Page tree icons
    'apps-pagetree-flight-landing' => [
        'provider' => SvgIconProvider::class,
        'source' => 'EXT:landing-pages/Resources/Public/Icons/apps-pagetree-flight-landing.svg',
    ],
    'apps-pagetree-flight-template' => [
        'provider' => SvgIconProvider::class,
        'source' => 'EXT:landing-pages/Resources/Public/Icons/apps-pagetree-flight-template.svg',
    ],
    
    // Content element icons
    'content-destination-pairs-menu' => [
        'provider' => SvgIconProvider::class,
        'source' => 'EXT:landing-pages/Resources/Public/Icons/content-destination-pairs-menu.svg',
    ],
    'content-flight-fromto' => [
        'provider' => SvgIconProvider::class,
        'source' => 'EXT:landing-pages/Resources/Public/Icons/content-flight-fromto.svg',
    ],
    
    // Domain model icons
    'tx_landingpages_domain_model_flightroute' => [
        'provider' => SvgIconProvider::class,
        'source' => 'EXT:landing-pages/Resources/Public/Icons/tx_landingpages_domain_model_flightroute.svg',
    ],
    'tx_landingpages_domain_model_templatemapping' => [
        'provider' => SvgIconProvider::class,
        'source' => 'EXT:landing-pages/Resources/Public/Icons/tx_landingpages_domain_model_templatemapping.svg',
    ],
    'tx_landingpages_domain_model_flightfromto' => [
        'provider' => SvgIconProvider::class,
        'source' => 'EXT:landing-pages/Resources/Public/Icons/tx_landingpages_domain_model_flightfromto.svg',
    ],
];
