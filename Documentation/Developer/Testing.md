# Testing Guide

## Overview

This guide covers testing strategies, test setup, and best practices for the Flight Landing Pages extension. The extension includes comprehensive unit and functional tests to ensure reliability and maintainability.

## Test Structure

### Directory Organization

```
Tests/
├── Unit/                           # Unit tests
│   ├── Domain/
│   │   ├── Model/                  # Domain model tests
│   │   └── Repository/             # Repository tests
│   ├── Service/                    # Service tests
│   ├── Middleware/                 # Middleware tests
│   ├── EventListener/              # Event listener tests
│   └── DataProcessing/             # Data processor tests
├── Functional/                     # Functional tests
│   ├── VirtualRoutes/              # Virtual route integration tests
│   ├── Middleware/                 # Middleware integration tests
│   └── Frontend/                   # Frontend rendering tests
└── Fixtures/                       # Test data and fixtures
    ├── Database/                   # Database fixtures
    ├── Files/                      # File fixtures
    └── Configuration/              # Configuration fixtures
```

## Test Environment Setup

### Prerequisites

- DDEV or similar development environment
- TYPO3 Testing Framework
- PHPUnit 9.5+
- PHP 8.1+

### Installation

```bash
# Install testing dependencies
ddev composer require --dev typo3/testing-framework

# Install PHPUnit
ddev composer require --dev phpunit/phpunit

# Install additional testing tools
ddev composer require --dev phpstan/phpstan
ddev composer require --dev friendsofphp/php-cs-fixer
```

### Configuration

#### phpunit.xml

```xml
<?xml version="1.0" encoding="UTF-8"?>
<phpunit
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:noNamespaceSchemaLocation="https://schema.phpunit.de/9.5/phpunit.xsd"
    bootstrap="vendor/typo3/testing-framework/Resources/Core/Build/UnitTestsBootstrap.php"
    colors="true"
    beStrictAboutTestsThatDoNotTestAnything="true"
    beStrictAboutOutputDuringTests="true"
    beStrictAboutTodoAnnotatedTests="true"
    convertErrorsToExceptions="true"
    convertWarningsToExceptions="true"
    forceCoversAnnotation="false"
    processIsolation="false"
    stopOnError="false"
    stopOnFailure="false"
    stopOnIncomplete="false"
    stopOnSkipped="false"
    verbose="false">

    <testsuites>
        <testsuite name="Unit Tests">
            <directory>Tests/Unit/</directory>
        </testsuite>
        <testsuite name="Functional Tests">
            <directory>Tests/Functional/</directory>
        </testsuite>
    </testsuites>

    <coverage>
        <include>
            <directory suffix=".php">Classes/</directory>
        </include>
        <exclude>
            <directory>Tests/</directory>
            <directory>vendor/</directory>
        </exclude>
    </coverage>
</phpunit>
```

## Unit Testing

### Domain Model Tests

#### FlightRoute Model Test

```php
<?php
namespace Bgs\LandingPages\Tests\Unit\Domain\Model;

use Bgs\LandingPages\Domain\Model\FlightRoute;
use TYPO3\TestingFramework\Core\Unit\UnitTestCase;

class FlightRouteTest extends UnitTestCase
{
    protected FlightRoute $subject;

    protected function setUp(): void
    {
        parent::setUp();
        $this->subject = new FlightRoute();
    }

    /**
     * @test
     */
    public function setOriginCodeSetsOriginCode(): void
    {
        $originCode = 'BER';
        $this->subject->setOriginCode($originCode);
        
        self::assertEquals($originCode, $this->subject->getOriginCode());
    }

    /**
     * @test
     */
    public function setOriginNameSetsOriginName(): void
    {
        $originName = 'Berlin Brandenburg Airport';
        $this->subject->setOriginName($originName);
        
        self::assertEquals($originName, $this->subject->getOriginName());
    }

    /**
     * @test
     */
    public function isActiveDefaultsToTrue(): void
    {
        self::assertTrue($this->subject->getIsActive());
    }

    /**
     * @test
     */
    public function setIsActiveSetsActiveStatus(): void
    {
        $this->subject->setIsActive(false);
        
        self::assertFalse($this->subject->getIsActive());
    }
}
```

### Service Tests

#### PlaceholderService Test

```php
<?php
namespace Bgs\LandingPages\Tests\Unit\Service;

use Bgs\LandingPages\Service\PlaceholderService;
use TYPO3\TestingFramework\Core\Unit\UnitTestCase;

class PlaceholderServiceTest extends UnitTestCase
{
    protected PlaceholderService $subject;

    protected function setUp(): void
    {
        parent::setUp();
        $this->subject = new PlaceholderService();
    }

    /**
     * @test
     */
    public function replacePlaceholdersReplacesBasicPlaceholders(): void
    {
        $content = 'Flight from [_origin_] to [_destination_]';
        $flightData = [
            'origin_name' => 'Berlin',
            'origin_type' => 'city',
            'destination_name' => 'Sofia',
            'destination_type' => 'city'
        ];

        $result = $this->subject->replacePlaceholders($content, $flightData);

        self::assertEquals('Flight from Berlin to Sofia', $result);
    }

    /**
     * @test
     */
    public function replacePlaceholdersHandlesAirportFormatting(): void
    {
        $content = 'From [_origin_] to [_destination_]';
        $flightData = [
            'origin_name' => 'Berlin Brandenburg Airport',
            'origin_code' => 'BER',
            'origin_type' => 'airport',
            'destination_name' => 'Sofia Airport',
            'destination_code' => 'SOF',
            'destination_type' => 'airport'
        ];

        $result = $this->subject->replacePlaceholders($content, $flightData);

        self::assertEquals('From Berlin Brandenburg Airport (BER) to Sofia Airport (SOF)', $result);
    }

    /**
     * @test
     */
    public function getAvailablePlaceholdersReturnsExpectedPlaceholders(): void
    {
        $placeholders = $this->subject->getAvailablePlaceholders();

        $expectedPlaceholders = [
            'origin', 'destination', 'origin_code', 'destination_code',
            'origin_type', 'destination_type', 'route_slug', 'price', 'currency'
        ];

        foreach ($expectedPlaceholders as $placeholder) {
            self::assertContains($placeholder, $placeholders);
        }
    }
}
```

#### VirtualRouteService Test

```php
<?php
namespace Bgs\LandingPages\Tests\Unit\Service;

use Bgs\LandingPages\Service\VirtualRouteService;
use Bgs\LandingPages\Service\TemplateResolutionService;
use TYPO3\CMS\Core\Site\Entity\Site;
use TYPO3\TestingFramework\Core\Unit\UnitTestCase;

class VirtualRouteServiceTest extends UnitTestCase
{
    protected VirtualRouteService $subject;
    protected TemplateResolutionService $templateResolutionServiceMock;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->templateResolutionServiceMock = $this->createMock(TemplateResolutionService::class);
        $this->subject = new VirtualRouteService($this->templateResolutionServiceMock);
    }

    /**
     * @test
     */
    public function isVirtualRouteReturnsFalseInitially(): void
    {
        self::assertFalse($this->subject->isVirtualRoute());
    }

    /**
     * @test
     */
    public function setVirtualRouteSetsVirtualRouteState(): void
    {
        $virtualRouteData = ['test' => 'data'];
        $this->subject->setVirtualRoute($virtualRouteData);
        
        self::assertTrue($this->subject->isVirtualRoute());
        self::assertEquals($virtualRouteData, $this->subject->getCurrentVirtualRoute());
    }

    /**
     * @test
     */
    public function clearVirtualRouteClearsState(): void
    {
        $this->subject->setVirtualRoute(['test' => 'data']);
        $this->subject->clearVirtualRoute();
        
        self::assertFalse($this->subject->isVirtualRoute());
        self::assertNull($this->subject->getCurrentVirtualRoute());
    }
}
```

### Middleware Tests

#### VirtualRouteHandler Test

```php
<?php
namespace Bgs\LandingPages\Tests\Unit\Middleware;

use Bgs\LandingPages\Middleware\VirtualRouteHandler;
use Bgs\LandingPages\Service\VirtualRouteService;
use Psr\Http\Message\ResponseInterface;
use Psr\Http\Message\ServerRequestInterface;
use Psr\Http\Message\UriInterface;
use Psr\Http\Server\RequestHandlerInterface;
use TYPO3\CMS\Core\Site\Entity\Site;
use TYPO3\TestingFramework\Core\Unit\UnitTestCase;

class VirtualRouteHandlerTest extends UnitTestCase
{
    protected VirtualRouteHandler $subject;
    protected VirtualRouteService $virtualRouteServiceMock;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->virtualRouteServiceMock = $this->createMock(VirtualRouteService::class);
        $this->subject = new VirtualRouteHandler($this->virtualRouteServiceMock);
    }

    /**
     * @test
     */
    public function processPassesThroughWhenNoSite(): void
    {
        $request = $this->createMock(ServerRequestInterface::class);
        $handler = $this->createMock(RequestHandlerInterface::class);
        $response = $this->createMock(ResponseInterface::class);

        $request->method('getAttribute')->with('site')->willReturn(null);
        $handler->expects(self::once())->method('handle')->willReturn($response);

        $result = $this->subject->process($request, $handler);

        self::assertSame($response, $result);
    }

    /**
     * @test
     */
    public function processPassesThroughWhenNoVirtualRoute(): void
    {
        $request = $this->createMock(ServerRequestInterface::class);
        $handler = $this->createMock(RequestHandlerInterface::class);
        $response = $this->createMock(ResponseInterface::class);
        $site = $this->createMock(Site::class);
        $uri = $this->createMock(UriInterface::class);

        $request->method('getAttribute')->with('site')->willReturn($site);
        $request->method('getUri')->willReturn($uri);
        $uri->method('getPath')->willReturn('/normal-page');

        $this->virtualRouteServiceMock
            ->expects(self::once())
            ->method('clearVirtualRoute');

        $this->virtualRouteServiceMock
            ->expects(self::once())
            ->method('detectVirtualRoute')
            ->willReturn(null);

        $handler->expects(self::once())->method('handle')->willReturn($response);

        $result = $this->subject->process($request, $handler);

        self::assertSame($response, $result);
    }
}
```

## Functional Testing

### Virtual Route Integration Tests

#### VirtualRouteIntegrationTest

```php
<?php
namespace Bgs\LandingPages\Tests\Functional\VirtualRoutes;

use TYPO3\CMS\Core\Core\Bootstrap;
use TYPO3\CMS\Core\Http\ServerRequest;
use TYPO3\CMS\Core\Http\Uri;
use TYPO3\TestingFramework\Core\Functional\FunctionalTestCase;

class VirtualRouteIntegrationTest extends FunctionalTestCase
{
    protected array $testExtensionsToLoad = [
        'typo3conf/ext/landing-pages'
    ];

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->importDataSet(__DIR__ . '/../../Fixtures/Database/pages.xml');
        $this->importDataSet(__DIR__ . '/../../Fixtures/Database/flight_routes.xml');
        
        Bootstrap::initializeLanguageObject();
    }

    /**
     * @test
     */
    public function virtualRouteIsDetectedCorrectly(): void
    {
        $request = new ServerRequest(new Uri('https://example.com/flights/ber-sof'));
        
        // Test virtual route detection
        // This would require setting up the full TYPO3 request pipeline
        // Implementation depends on specific testing requirements
        
        self::assertTrue(true); // Placeholder assertion
    }

    /**
     * @test
     */
    public function virtualRouteGeneratesCorrectContent(): void
    {
        // Test that virtual routes generate expected content
        // This would involve full frontend rendering tests
        
        self::assertTrue(true); // Placeholder assertion
    }
}
```

### Database Fixtures

#### pages.xml

```xml
<?xml version="1.0" encoding="utf-8"?>
<dataset>
    <pages>
        <uid>1</uid>
        <pid>0</pid>
        <title>Root Page</title>
        <doktype>1</doktype>
        <is_siteroot>1</is_siteroot>
        <slug>/</slug>
    </pages>
    <pages>
        <uid>2</uid>
        <pid>1</pid>
        <title>Flight Template</title>
        <doktype>200</doktype>
        <slug>/flight-template</slug>
    </pages>
    <pages>
        <uid>3</uid>
        <pid>1</pid>
        <title>Flights</title>
        <doktype>201</doktype>
        <slug>/flights</slug>
        <tx_landingpages_template_page>2</tx_landingpages_template_page>
    </pages>
</dataset>
```

#### flight_routes.xml

```xml
<?xml version="1.0" encoding="utf-8"?>
<dataset>
    <tx_landingpages_domain_model_flightroute>
        <uid>1</uid>
        <pid>3</pid>
        <origin_code>BER</origin_code>
        <origin_name>Berlin Brandenburg Airport</origin_name>
        <origin_type>airport</origin_type>
        <destination_code>SOF</destination_code>
        <destination_name>Sofia Airport</destination_name>
        <destination_type>airport</destination_type>
        <route_slug>ber-sof</route_slug>
        <is_active>1</is_active>
    </tx_landingpages_domain_model_flightroute>
</dataset>
```

## Test Execution

### Running Tests

#### All Tests
```bash
ddev composer test
```

#### Unit Tests Only
```bash
ddev composer test:unit
# or
ddev exec vendor/bin/phpunit Tests/Unit/
```

#### Functional Tests Only
```bash
ddev composer test:functional
# or
ddev exec vendor/bin/phpunit Tests/Functional/
```

#### Specific Test Class
```bash
ddev exec vendor/bin/phpunit Tests/Unit/Service/PlaceholderServiceTest.php
```

#### With Coverage
```bash
ddev exec vendor/bin/phpunit --coverage-html coverage-report/
```

### Composer Scripts

Add these scripts to `composer.json`:

```json
{
    "scripts": {
        "test": "phpunit",
        "test:unit": "phpunit Tests/Unit/",
        "test:functional": "phpunit Tests/Functional/",
        "test:coverage": "phpunit --coverage-html coverage-report/",
        "cs:check": "php-cs-fixer fix --dry-run --diff",
        "cs:fix": "php-cs-fixer fix",
        "phpstan": "phpstan analyse"
    }
}
```

## Code Quality

### PHP CS Fixer Configuration

#### .php-cs-fixer.php

```php
<?php

$config = new PhpCsFixer\Config();
return $config
    ->setRules([
        '@PSR12' => true,
        'array_syntax' => ['syntax' => 'short'],
        'ordered_imports' => true,
        'no_unused_imports' => true,
        'trailing_comma_in_multiline' => true,
    ])
    ->setFinder(
        PhpCsFixer\Finder::create()
            ->in(__DIR__ . '/Classes')
            ->in(__DIR__ . '/Tests')
    );
```

### PHPStan Configuration

#### phpstan.neon

```neon
parameters:
    level: 5
    paths:
        - Classes
        - Tests
    excludePaths:
        - Tests/Fixtures
    ignoreErrors:
        - '#Call to an undefined method.*#'
```

## Testing Best Practices

### Unit Test Guidelines

1. **Test One Thing**: Each test should verify one specific behavior
2. **Descriptive Names**: Test method names should describe what is being tested
3. **Arrange-Act-Assert**: Structure tests with clear setup, execution, and verification
4. **Mock Dependencies**: Use mocks for external dependencies
5. **Test Edge Cases**: Include tests for boundary conditions and error cases

### Functional Test Guidelines

1. **Real Database**: Use actual database operations with fixtures
2. **Complete Scenarios**: Test full user workflows
3. **Environment Isolation**: Each test should be independent
4. **Performance Awareness**: Keep functional tests reasonably fast
5. **Data Cleanup**: Ensure tests clean up after themselves

### Test Data Management

1. **Fixtures**: Use XML fixtures for consistent test data
2. **Factories**: Create test data factories for complex objects
3. **Minimal Data**: Use only the data necessary for each test
4. **Realistic Data**: Test data should represent real-world scenarios

### Continuous Integration

#### GitHub Actions Example

```yaml
name: Tests

on: [push, pull_request]

jobs:
  test:
    runs-on: ubuntu-latest
    
    strategy:
      matrix:
        php-version: [8.1, 8.2]
        typo3-version: [12.4]
    
    steps:
      - uses: actions/checkout@v2
      
      - name: Setup PHP
        uses: shivammathur/setup-php@v2
        with:
          php-version: ${{ matrix.php-version }}
          
      - name: Install dependencies
        run: composer install
        
      - name: Run tests
        run: composer test
        
      - name: Code style check
        run: composer cs:check
        
      - name: Static analysis
        run: composer phpstan
```

## Debugging Tests

### Debug Techniques

1. **Verbose Output**: Use `--verbose` flag for detailed test output
2. **Stop on Failure**: Use `--stop-on-failure` to halt on first failure
3. **Filter Tests**: Use `--filter` to run specific tests
4. **Debug Output**: Use `var_dump()` or `error_log()` for debugging
5. **Xdebug**: Configure Xdebug for step-through debugging

### Common Issues

1. **Database State**: Ensure database is properly reset between tests
2. **Global State**: Avoid relying on global variables or state
3. **Time Dependencies**: Use fixed timestamps for time-dependent tests
4. **File System**: Clean up temporary files created during tests
5. **Memory Limits**: Monitor memory usage in functional tests

---

This testing guide provides a comprehensive foundation for maintaining high code quality and reliability in the Flight Landing Pages extension.
