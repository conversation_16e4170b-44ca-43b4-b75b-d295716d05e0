# Flight Landing Pages Extension - Developer Documentation

## Table of Contents

1. [Architecture Overview](#architecture-overview)
2. [Core Components](#core-components)
3. [Virtual Routes System](#virtual-routes-system)
4. [Data Flow](#data-flow)
5. [Extension Points](#extension-points)
6. [API Reference](#api-reference)
7. [Development Setup](#development-setup)
8. [Testing](#testing)
9. [Contributing](#contributing)

## Architecture Overview

The Flight Landing Pages extension implements a sophisticated virtual routing system for TYPO3 that creates dynamic landing pages for flight routes without requiring physical pages in the page tree.

### Key Architectural Principles

- **Page Type System**: Uses custom page types (doktype 200 for templates, 201 for landing pages)
- **Virtual Routes**: Dynamic URL generation without physical pages
- **Middleware-based Processing**: Request interception and modification
- **Event-driven Architecture**: PSR-14 events for extensibility
- **Service-oriented Design**: Clear separation of concerns

### Technology Stack

- **TYPO3 v12.4+**: Core framework
- **PHP 8.1+**: Programming language
- **PSR-14 Events**: Event system
- **Dependency Injection**: Service configuration
- **Fluid Templates**: Frontend rendering
- **Extbase/Doctrine**: Data persistence

## Core Components

### Domain Models

#### FlightRoute
Primary entity representing a flight route between two locations.

**Location**: `Classes/Domain/Model/FlightRoute.php`

**Properties**:
- `originCode` (string): Airport/city code (e.g., "BER")
- `originName` (string): Human-readable name (e.g., "Berlin Brandenburg Airport")
- `originType` (string): Type - airport|city|country
- `destinationCode` (string): Destination airport/city code
- `destinationName` (string): Destination human-readable name
- `destinationType` (string): Destination type
- `routeSlug` (string): URL slug (e.g., "ber-sof")
- `isActive` (bool): Route availability status

#### VirtualRouteContext
Entity for managing virtual route state during request processing.

**Location**: `Classes/Domain/Model/VirtualRouteContext.php`

**Purpose**: Transfers virtual route data between middleware and event listeners.

### Repositories

#### FlightRouteRepository
**Location**: `Classes/Domain/Repository/FlightRouteRepository.php`

**Key Methods**:
- `findAllActive()`: Get all active routes
- `findByRouteSlug()`: Find route by URL slug
- `findByLandingPage()`: Get routes for specific landing page

### Services

#### VirtualRouteService
**Location**: `Classes/Service/VirtualRouteService.php`

**Core service for virtual route processing**:
- Route detection and matching
- Virtual page generation
- Placeholder processing
- Template page loading

**Key Methods**:
- `detectVirtualRoute(string $path, Site $site): ?array`
- `generateVirtualPage(array $templatePage, array $flightRoute): array`
- `processPlaceholdersInContent(string $content, array $flightRoute): string`

#### PlaceholderService
**Location**: `Classes/Service/PlaceholderService.php`

**Handles placeholder replacement in content**:
- Pattern: `[_placeholder_name_]`
- Type-specific formatting (airports show code+name, cities show name only)
- Recursive processing for nested content

#### UrlGenerationService
**Location**: `Classes/Service/UrlGenerationService.php`

**URL generation and parsing**:
- Generate URLs from FlightRoute models
- Parse virtual route URLs
- Site-aware URL building

#### SiteConfigurationService
**Location**: `Classes/Service/SiteConfigurationService.php`

**Multi-site support**:
- Site detection and configuration
- Site-specific route filtering
- Root page resolution

### Controllers

#### DestinationsMenuController
**Location**: `Classes/Controller/DestinationsMenuController.php`

**Frontend plugin controller**:
- Renders destination lists
- Supports multiple display modes (list, grid, cards)
- Filtering and pagination

#### VirtualRouteFrontendController
**Location**: `Classes/Controller/VirtualRouteFrontendController.php`

**Custom frontend controller extending TypoScriptFrontendController**:
- Handles virtual route rendering
- Processes template page content
- Maintains TYPO3 standard functionality

#### Backend Controllers
**Location**: `Classes/Controller/Backend/`

- `CsvExportController`: CSV export functionality
- Various backend management interfaces

### Middleware

#### VirtualRouteHandler
**Location**: `Classes/Middleware/VirtualRouteHandler.php`

**Primary middleware (runs before PageResolver)**:
- Detects virtual routes
- Modifies request to point to template page
- Sets up virtual route context

**Processing Order**: Before `typo3/cms-frontend/page-resolver`

#### DynamicArgumentsMiddleware
**Location**: `Classes/Middleware/DynamicArgumentsMiddleware.php`

**Secondary middleware (runs after PageArgumentValidator)**:
- Adds dynamic routing arguments
- Manages cHash for proper caching
- Handles route-specific parameters

**Processing Order**: After `typo3/cms-frontend/page-argument-validator`

### Event Listeners

#### VirtualRouteDetectionListener
**Location**: `Classes/EventListener/VirtualRouteDetectionListener.php`

**Event**: `BeforePageIsResolvedEvent`
**Purpose**: Detects and stores virtual route context

#### VirtualPageReplacementListener
**Location**: `Classes/EventListener/VirtualPageReplacementListener.php`

**Event**: `AfterPageWithRootLineIsResolvedEvent`
**Purpose**: Replaces resolved page with virtual page data

#### VirtualContentProcessingListener
**Location**: `Classes/EventListener/VirtualContentProcessingListener.php`

**Event**: `AfterCacheableContentIsGeneratedEvent`
**Purpose**: Processes placeholders in generated content

#### PagePreviewEventListener
**Location**: `Classes/EventListener/PagePreviewEventListener.php`

**Event**: `ModifyPageLayoutContentEvent`
**Purpose**: Enhances backend page preview for landing pages

### Data Processors

#### FlightRouteProcessor
**Location**: `Classes/DataProcessing/FlightRouteProcessor.php`

**TypoScript data processor for Flight Landing Pages (doktype 201)**:
- Loads flight route data
- Processes placeholders in content elements
- Makes flight data available to templates

#### VirtualRouteDataProcessor
**Location**: `Classes/DataProcessing/VirtualRouteDataProcessor.php`

**Alternative data processor for virtual route handling**:
- Processes virtual route data from global state
- Fallback for template page processing

#### DestinationPairsMenuProcessor
**Location**: `Classes/DataProcessing/DestinationPairsMenuProcessor.php`

**Data processor for destination menu content elements**:
- Loads and filters flight routes
- Supports pagination and search
- Multiple display modes

### Commands

#### UpdateSlugCommand
**Location**: `Classes/Command/UpdateSlugCommand.php`

**CLI command for slug maintenance**:
```bash
ddev typo3cms landingpages:updateslug
```

#### UpdateChildSlugsCommand
**Location**: `Classes/Command/UpdateChildSlugsCommand.php`

**CLI command for updating child page slugs**:
```bash
ddev typo3cms landingpages:updatechildslugs
```

### Hooks

#### DataHandlerHook
**Location**: `Classes/Hooks/DataHandlerHook.php`

**TYPO3 DataHandler hook for automatic slug updates**:
- Updates route slugs when parent pages change
- Maintains URL consistency

### XML Sitemap Integration

#### FlightRoutesXmlSitemapDataProvider
**Location**: `Classes/XmlSitemap/FlightRoutesXmlSitemapDataProvider.php`

**Sitemap provider for virtual routes**:
- Generates sitemap entries for all flight routes
- Inherits priority and change frequency from template pages
- Multi-language support

### User Functions

#### ContentElementRestriction
**Location**: `Classes/UserFunctions/ContentElementRestriction.php`

**TCA user function for restricting content elements on specific page types**.

#### SlugModifier
**Location**: `Classes/UserFunctions/SlugModifier.php`

**TCA user function for custom slug generation and validation**.

#### VirtualRouteContentRenderer
**Location**: `Classes/UserFunc/VirtualRouteContentRenderer.php`

**TypoScript user function for rendering virtual route content**.

### Preview Renderers

#### DestinationPairsMenuPreviewRenderer
**Location**: `Classes/Preview/DestinationPairsMenuPreviewRenderer.php`

**Backend preview renderer for destination pairs menu content elements**.

## Virtual Routes System

The virtual routes system is the core innovation of this extension, allowing dynamic content generation without physical pages.

### How Virtual Routes Work

1. **Request Interception**: `VirtualRouteHandler` middleware detects virtual route patterns
2. **Route Matching**: System matches URL against flight route database
3. **Template Loading**: Loads corresponding template page (doktype 200)
4. **Content Processing**: Processes template content with flight-specific data
5. **Response Generation**: Returns processed content as if it were a physical page

### URL Structure

Virtual routes follow this pattern:
```
{site-base}/{landing-page-slug}/{route-slug}
```

Example:
```
https://example.com/flights/ber-sof
```

Where:
- `flights` = Landing page slug (doktype 201)
- `ber-sof` = Flight route slug

### Route Detection Algorithm

```php
// Simplified detection logic
$pathParts = explode('/', trim($path, '/'));
$routeSlug = end($pathParts);
array_pop($pathParts);
$landingPagePath = implode('/', $pathParts);

// Find landing page by path
$landingPage = findLandingPageByPath($landingPagePath);

// Find matching flight route
$flightRoute = findFlightRoute($routeSlug, $landingPage['uid']);
```

## Data Flow

### Request Processing Flow

```mermaid
graph TD
    A[HTTP Request] --> B[VirtualRouteHandler Middleware]
    B --> C{Virtual Route?}
    C -->|Yes| D[Modify Request to Template Page]
    C -->|No| E[Continue Normal Processing]
    D --> F[PageResolver]
    F --> G[VirtualRouteDetectionListener]
    G --> H[VirtualPageReplacementListener]
    H --> I[Template Page Loaded]
    I --> J[FlightRouteProcessor]
    J --> K[Content Generation]
    K --> L[VirtualContentProcessingListener]
    L --> M[Placeholder Processing]
    M --> N[Final Response]
    E --> O[Normal TYPO3 Processing]
```

### Data Processing Pipeline

1. **Middleware Phase**: Route detection and request modification
2. **Event Phase**: Virtual route context setup
3. **Page Resolution**: Template page loading
4. **Data Processing**: Flight data integration
5. **Content Generation**: Template rendering
6. **Post-processing**: Placeholder replacement

## Extension Points

### Custom Placeholder Types

Extend `PlaceholderService` to add custom placeholders:

```php
class CustomPlaceholderService extends PlaceholderService
{
    protected function getPlaceholderValue(string $placeholder, array $flightRoute): string
    {
        switch ($placeholder) {
            case 'custom_field':
                return $this->getCustomValue($flightRoute);
            default:
                return parent::getPlaceholderValue($placeholder, $flightRoute);
        }
    }
}
```

### Custom Route Detection

Override `VirtualRouteService::detectVirtualRoute()` for custom routing logic:

```php
class CustomVirtualRouteService extends VirtualRouteService
{
    public function detectVirtualRoute(string $path, Site $site): ?array
    {
        // Custom detection logic
        if ($this->isCustomRoute($path)) {
            return $this->handleCustomRoute($path, $site);
        }
        
        return parent::detectVirtualRoute($path, $site);
    }
}
```

### Event Listeners

Register custom event listeners in `Services.yaml`:

```yaml
Your\Extension\EventListener\CustomListener:
  tags:
    - name: event.listener
      identifier: 'your-custom-listener'
      event: TYPO3\CMS\Frontend\Event\AfterCacheableContentIsGeneratedEvent
```

### Data Processors

Create custom data processors for specialized content:

```php
class CustomFlightDataProcessor implements DataProcessorInterface
{
    public function process(
        ContentObjectRenderer $cObj,
        array $contentObjectConfiguration,
        array $processorConfiguration,
        array $processedData
    ): array {
        // Custom processing logic
        return $processedData;
    }
}
```

## API Reference

### Service APIs

#### VirtualRouteService

```php
// Check if current request is virtual route
$isVirtual = $virtualRouteService->isVirtualRoute();

// Get current virtual route data
$routeData = $virtualRouteService->getCurrentVirtualRoute();

// Process placeholders in content
$processed = $virtualRouteService->processPlaceholdersInContent($content, $flightRoute);
```

#### UrlGenerationService

```php
// Generate URL from FlightRoute model
$url = $urlGenerationService->generateRouteUrl($flightRoute, $landingPage);

// Parse virtual route URL
$components = $urlGenerationService->parseVirtualRouteUrl($url);
```

#### PlaceholderService

```php
// Replace placeholders in content
$processed = $placeholderService->replacePlaceholders($content, $flightRouteData);

// Get available placeholders
$placeholders = $placeholderService->getAvailablePlaceholders();
```

### Repository APIs

#### FlightRouteRepository

```php
// Find all active routes
$routes = $flightRouteRepository->findAllActive();

// Find by route slug
$route = $flightRouteRepository->findByRouteSlug('ber-sof');

// Find by landing page
$routes = $flightRouteRepository->findByLandingPage($landingPageUid);
```

### ViewHelper APIs

#### ReplacePlaceholderViewHelper

```html
<!-- In Fluid templates -->
<lp:replacePlaceholder content="{templateContent}" data="{flightRouteData}" />
```

## Development Setup

### Prerequisites

- DDEV or similar local development environment
- TYPO3 v12.4+
- PHP 8.1+
- Composer

### Installation

```bash
# Clone repository
git clone [repository-url]

# Start DDEV
ddev start

# Install dependencies
ddev composer install

# Setup TYPO3
ddev typo3cms setup

# Activate extension
ddev typo3cms extension:activate landing-pages
```

### Database Setup

```bash
# Update database schema
ddev typo3cms database:updateschema
```

### Development Commands

```bash
# Run tests
ddev composer test

# Code quality checks
ddev composer cs:check
ddev composer cs:fix

# Package extension
ddev composer package
```

## Testing

### Unit Tests

Located in `Tests/Unit/`, covering:
- Domain models
- Services
- Repositories
- Utilities

### Functional Tests

Located in `Tests/Functional/`, covering:
- Virtual route processing
- Middleware functionality
- Event listeners
- Integration scenarios

### Running Tests

```bash
# All tests
ddev composer test

# Unit tests only
ddev composer test:unit

# Functional tests only
ddev composer test:functional
```

## Contributing

### Code Standards

- Follow TYPO3 coding standards
- Use PSR-12 for PHP code style
- Document all public methods
- Write tests for new functionality

### Pull Request Process

1. Fork the repository
2. Create feature branch
3. Implement changes with tests
4. Ensure code quality checks pass
5. Submit pull request with description

### Development Guidelines

- Use dependency injection for services
- Follow TYPO3 v12 best practices
- Maintain backward compatibility
- Update documentation for new features

---

## Additional Documentation

### Core Documentation
- **[Architecture Guide](Architecture.md)** - Detailed architectural overview and design patterns
- **[API Reference](API.md)** - Comprehensive API documentation for all services and components
- **[Testing Guide](Testing.md)** - Testing strategies, setup, and best practices

### Specialized Guides
- **[Integration Guide](../Integration.md)** - Site integration and template development
- **[Configuration Reference](../Configuration.md)** - Complete configuration options
- **[Troubleshooting Guide](../Troubleshooting.md)** - Common issues and solutions

### Quick Links
- [Extension Overview](../../README.md)
- [Installation Guide](../Installation.md)
- [User Manual](../User.md)
- [Changelog](../Changelog.md)
