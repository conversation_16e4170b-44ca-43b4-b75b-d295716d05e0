# Developer Troubleshooting Guide

## Overview

This guide covers common development issues, debugging techniques, and solutions for the Flight Landing Pages extension.

## Common Development Issues

### 1. Virtual Routes Not Working

#### Symptoms
- Virtual routes return 404 errors
- Template pages are accessed directly instead of virtual routes
- Middleware not processing requests

#### Debugging Steps

**Check Middleware Registration**
```bash
# Verify middleware is registered
ddev typo3cms debug:middleware
```

Look for:
- `landing-pages/virtual-route-handler`
- `landing-pages/dynamic-arguments`

**Check Service Registration**
```bash
# Verify services are registered
ddev typo3cms debug:container | grep LandingPages
```

**Enable Debug Logging**
```php
// In ext_localconf.php or AdditionalConfiguration.php
$GLOBALS['TYPO3_CONF_VARS']['LOG']['Bgs']['LandingPages'] = [
    'writerConfiguration' => [
        \TYPO3\CMS\Core\Log\LogLevel::DEBUG => [
            \TYPO3\CMS\Core\Log\Writer\FileWriter::class => [
                'logFile' => 'typo3temp/logs/landing-pages.log'
            ]
        ]
    ]
];
```

**Check Route Detection**
```php
// Add debug output in VirtualRouteService::detectVirtualRoute()
public function detectVirtualRoute(string $path, Site $site): ?array
{
    error_log("Checking path: " . $path);
    $result = // ... existing logic
    error_log("Detection result: " . json_encode($result));
    return $result;
}
```

#### Common Solutions

1. **Clear All Caches**
```bash
ddev typo3cms cache:flush
```

2. **Check Middleware Order**
Ensure middleware is positioned correctly in `Configuration/RequestMiddlewares.php`:
```php
return [
    'frontend' => [
        'landing-pages/virtual-route-handler' => [
            'target' => \Bgs\LandingPages\Middleware\VirtualRouteHandler::class,
            'before' => ['typo3/cms-frontend/page-resolver'],
        ],
    ],
];
```

3. **Verify Database Schema**
```bash
ddev typo3cms database:updateschema
```

### 2. Placeholder Replacement Not Working

#### Symptoms
- Placeholders appear as literal text
- Partial placeholder replacement
- Wrong data in placeholders

#### Debugging Steps

**Check Placeholder Service**
```php
// Test placeholder service directly
$placeholderService = GeneralUtility::makeInstance(PlaceholderService::class);
$result = $placeholderService->replacePlaceholders('[_origin_]', $flightData);
var_dump($result);
```

**Verify Flight Route Data**
```php
// In FlightRouteProcessor or VirtualRouteService
error_log("Flight route data: " . json_encode($flightRouteData));
```

**Check Content Processing**
```php
// In VirtualContentProcessingListener
public function __invoke(AfterCacheableContentIsGeneratedEvent $event): void
{
    error_log("Processing content: " . substr($controller->content, 0, 200));
    // ... existing logic
    error_log("Processed content: " . substr($processedContent, 0, 200));
}
```

#### Common Solutions

1. **Verify Placeholder Format**
Ensure placeholders use correct format: `[_placeholder_name_]`

2. **Check Data Structure**
Verify flight route data has expected structure:
```php
$expectedStructure = [
    'origin_name' => 'Berlin',
    'origin_code' => 'BER',
    'origin_type' => 'airport',
    // ... etc
];
```

3. **Test Placeholder Service**
```php
// Unit test for placeholder service
$content = 'From [_origin_] to [_destination_]';
$data = ['origin_name' => 'Berlin', 'destination_name' => 'Sofia'];
$result = $placeholderService->replacePlaceholders($content, $data);
// Should return: "From Berlin to Sofia"
```

### 3. Data Processor Issues

#### Symptoms
- Flight data not available in templates
- Empty `{flightRouteData}` variable
- Data processor not executing

#### Debugging Steps

**Check TypoScript Configuration**
```typoscript
# Verify data processor is configured
page.10.dataProcessing {
    10 = Bgs\LandingPages\DataProcessing\FlightRouteProcessor
    10 {
        as = flightRouteData
    }
}
```

**Debug Data Processor**
```php
// In FlightRouteProcessor::process()
public function process(/* ... */): array
{
    error_log("Data processor called for page: " . ($pageData['uid'] ?? 'unknown'));
    error_log("Page doktype: " . ($pageData['doktype'] ?? 'unknown'));
    
    // ... existing logic
    
    error_log("Processed data: " . json_encode($processedData[$targetVariableName]));
    return $processedData;
}
```

**Check Page Type**
Ensure page has correct doktype (201 for landing pages):
```sql
SELECT uid, title, doktype FROM pages WHERE uid = [page_uid];
```

#### Common Solutions

1. **Verify Page Type**
Data processor only runs on Flight Landing Pages (doktype 201)

2. **Check Template Page Reference**
```sql
SELECT tx_landingpages_template_page FROM pages WHERE uid = [landing_page_uid];
```

3. **Test Data Processor Directly**
```php
$processor = GeneralUtility::makeInstance(FlightRouteProcessor::class);
$result = $processor->process($cObj, [], ['as' => 'flightRouteData'], $processedData);
```

### 4. Event Listener Problems

#### Symptoms
- Event listeners not executing
- Virtual route context not set
- Page replacement not working

#### Debugging Steps

**Check Event Registration**
```yaml
# Verify in Services.yaml
services:
  Bgs\LandingPages\EventListener\VirtualRouteDetectionListener:
    tags:
      - name: event.listener
        identifier: 'landing-pages-virtual-route-detection'
        event: TYPO3\CMS\Frontend\Event\BeforePageIsResolvedEvent
```

**Debug Event Execution**
```php
// In event listener
public function __invoke(BeforePageIsResolvedEvent $event): void
{
    error_log("Event listener called: " . get_class($this));
    // ... existing logic
}
```

**Check Event Order**
Events must execute in correct order:
1. `BeforePageIsResolvedEvent`
2. `AfterPageWithRootLineIsResolvedEvent`
3. `AfterCacheableContentIsGeneratedEvent`

#### Common Solutions

1. **Verify Service Configuration**
```bash
ddev typo3cms debug:container | grep EventListener
```

2. **Check Event Names**
Ensure event names match TYPO3 core events exactly

3. **Test Event Isolation**
```php
// Test individual event listener
$listener = GeneralUtility::makeInstance(VirtualRouteDetectionListener::class);
$event = new BeforePageIsResolvedEvent($request, $routeResult);
$listener($event);
```

### 5. Database and Repository Issues

#### Symptoms
- Flight routes not found
- Repository methods returning empty results
- Database queries failing

#### Debugging Steps

**Check Database Schema**
```bash
ddev typo3cms database:updateschema --dry-run
```

**Test Repository Directly**
```php
$repository = GeneralUtility::makeInstance(FlightRouteRepository::class);
$routes = $repository->findAllActive();
error_log("Found routes: " . count($routes));
```

**Check Database Content**
```sql
SELECT * FROM tx_landingpages_domain_model_flightroute WHERE deleted = 0;
```

**Debug Query Builder**
```php
// In repository method
$queryBuilder = GeneralUtility::makeInstance(ConnectionPool::class)
    ->getQueryBuilderForTable('tx_landingpages_domain_model_flightroute');

$query = $queryBuilder
    ->select('*')
    ->from('tx_landingpages_domain_model_flightroute')
    ->where(/* conditions */);

error_log("SQL: " . $query->getSQL());
error_log("Parameters: " . json_encode($query->getParameters()));
```

#### Common Solutions

1. **Update Database Schema**
```bash
ddev typo3cms database:updateschema
```

2. **Check Record Storage Page**
Ensure repository respects storage page settings

3. **Verify Table Structure**
```sql
DESCRIBE tx_landingpages_domain_model_flightroute;
```

## Debugging Techniques

### 1. Logging

#### Enable Extension Logging
```php
// In ext_localconf.php
$GLOBALS['TYPO3_CONF_VARS']['LOG']['Bgs']['LandingPages'] = [
    'writerConfiguration' => [
        \TYPO3\CMS\Core\Log\LogLevel::DEBUG => [
            \TYPO3\CMS\Core\Log\Writer\FileWriter::class => [
                'logFile' => 'typo3temp/logs/landing-pages.log'
            ]
        ]
    ]
];
```

#### Use Logger in Classes
```php
use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerAwareTrait;

class VirtualRouteService implements LoggerAwareInterface
{
    use LoggerAwareTrait;

    public function detectVirtualRoute(string $path, Site $site): ?array
    {
        $this->logger->debug('Detecting virtual route', ['path' => $path]);
        // ... logic
    }
}
```

### 2. Debug Output

#### Template Debug
```html
<!-- In Fluid templates -->
<f:debug>{_all}</f:debug>
<f:debug>{flightRouteData}</f:debug>
```

#### PHP Debug
```php
// Quick debug output
error_log("Debug: " . json_encode($data));
var_dump($data); // For development only
```

### 3. TYPO3 Debug Tools

#### Admin Panel
Enable admin panel for frontend debugging:
```php
// In AdditionalConfiguration.php
$GLOBALS['TYPO3_CONF_VARS']['BE']['debug'] = true;
```

#### Debug Mode
```php
// Enable debug mode
$GLOBALS['TYPO3_CONF_VARS']['FE']['debug'] = true;
```

### 4. Browser Developer Tools

#### Network Tab
- Check for 404 errors on virtual routes
- Verify correct URLs are generated
- Monitor AJAX requests

#### Console
- Check for JavaScript errors
- Monitor console.log output
- Test frontend functionality

## Performance Debugging

### 1. Slow Virtual Routes

#### Profiling
```php
// Add timing to critical methods
$start = microtime(true);
// ... method logic
$end = microtime(true);
error_log("Method execution time: " . ($end - $start) . " seconds");
```

#### Database Query Optimization
```php
// Log slow queries
$GLOBALS['TYPO3_CONF_VARS']['DB']['Connections']['Default']['initCommands'] = 
    'SET SESSION long_query_time = 0.1; SET SESSION log_queries_not_using_indexes = ON;';
```

### 2. Memory Issues

#### Memory Monitoring
```php
// Monitor memory usage
error_log("Memory usage: " . memory_get_usage(true) / 1024 / 1024 . " MB");
error_log("Peak memory: " . memory_get_peak_usage(true) / 1024 / 1024 . " MB");
```

#### Object Cleanup
```php
// Ensure proper cleanup
unset($largeObjects);
gc_collect_cycles();
```

## Testing and Validation

### 1. Unit Test Debugging

#### Run Specific Tests
```bash
ddev exec vendor/bin/phpunit Tests/Unit/Service/VirtualRouteServiceTest.php::testDetectVirtualRoute
```

#### Debug Test Data
```php
// In test methods
protected function setUp(): void
{
    parent::setUp();
    error_log("Test setup completed");
}
```

### 2. Functional Test Issues

#### Database State
```php
// Check database state in tests
protected function tearDown(): void
{
    $this->assertDatabaseHasTable('tx_landingpages_domain_model_flightroute');
    parent::tearDown();
}
```

#### Fixture Loading
```php
// Verify fixtures are loaded
$this->importDataSet(__DIR__ . '/Fixtures/flight_routes.xml');
$count = $this->getDatabaseConnection()
    ->selectCount('*', 'tx_landingpages_domain_model_flightroute');
$this->assertGreaterThan(0, $count);
```

## Environment-Specific Issues

### 1. DDEV Problems

#### Container Issues
```bash
# Restart DDEV
ddev restart

# Rebuild containers
ddev delete
ddev start
```

#### Permission Issues
```bash
# Fix file permissions
ddev exec chmod -R 755 typo3temp/
ddev exec chmod -R 755 var/
```

### 2. Production Issues

#### Cache Problems
```bash
# Clear all caches
typo3cms cache:flush

# Warm up caches
typo3cms cache:warmup
```

#### File Permissions
```bash
# Set correct permissions
chown -R www-data:www-data typo3temp/
chmod -R 755 typo3temp/
```

## Getting Help

### 1. Debug Information Collection

When reporting issues, include:
- TYPO3 version
- PHP version
- Extension version
- Error messages
- Log files
- Steps to reproduce

### 2. Log Files to Check

- `typo3temp/logs/typo3.log`
- `typo3temp/logs/landing-pages.log`
- Web server error logs
- PHP error logs

### 3. Useful Commands

```bash
# Check extension status
ddev typo3cms extension:list | grep landing-pages

# Database schema status
ddev typo3cms database:updateschema --dry-run

# Clear specific caches
ddev typo3cms cache:flush --group=pages
ddev typo3cms cache:flush --group=system

# Check configuration
ddev typo3cms configuration:show
```

---

This troubleshooting guide should help developers quickly identify and resolve common issues with the Flight Landing Pages extension.
