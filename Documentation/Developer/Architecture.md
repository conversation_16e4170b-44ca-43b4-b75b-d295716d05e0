# Extension Architecture

## Overview

The Flight Landing Pages extension implements a sophisticated virtual routing system that creates dynamic landing pages without requiring physical pages in the TYPO3 page tree. This document provides an in-depth look at the architectural decisions and design patterns used.

## Architectural Patterns

### 1. Page Type System

The extension introduces two custom page types (doktypes) that work together:

#### Template Pages (doktype 200)
- **Purpose**: Store content elements and layout templates
- **Characteristics**:
  - Not directly accessible via frontend URLs
  - Contains normal TYPO3 content elements with placeholders
  - Serves as the content template for virtual routes
  - Can be organized in storage folders

#### Landing Pages (doktype 201)
- **Purpose**: Define URL patterns and manage flight routes
- **Characteristics**:
  - Contains URL pattern configuration
  - References template pages
  - Manages flight route records as sub-pages
  - Generates virtual routes based on child flight routes

### 2. Virtual Routes System

Virtual routes allow serving dynamic content at SEO-friendly URLs without creating physical pages.

#### Key Components:
- **Route Detection**: Pattern matching against incoming URLs
- **Template Resolution**: Loading appropriate template pages
- **Content Processing**: Placeholder replacement with dynamic data
- **Response Generation**: Standard TYPO3 page rendering

#### Benefits:
- **Scalability**: Thousands of routes without database bloat
- **SEO**: Clean, meaningful URLs
- **Performance**: Efficient caching and processing
- **Maintenance**: Single template for multiple routes

### 3. Middleware Architecture

The extension uses TYPO3's middleware system for request processing:

```
Request → VirtualRouteHandler → PageResolver → DynamicArgumentsMiddleware → Standard TYPO3 Processing
```

#### VirtualRouteHandler (Position: before page-resolver)
- Detects virtual route patterns
- Modifies request to point to template page
- Sets up virtual route context

#### DynamicArgumentsMiddleware (Position: after page-argument-validator)
- Adds dynamic routing arguments
- Manages cHash for proper caching
- Handles route-specific parameters

### 4. Event-Driven Processing

The extension leverages TYPO3's PSR-14 event system for extensibility:

#### Event Flow:
1. `BeforePageIsResolvedEvent` → Virtual route detection
2. `AfterPageWithRootLineIsResolvedEvent` → Page replacement
3. `AfterCacheableContentIsGeneratedEvent` → Content processing

#### Benefits:
- **Extensibility**: Easy to add custom processing
- **Decoupling**: Loose coupling between components
- **Testability**: Individual event listeners can be tested

### 5. Service-Oriented Design

Core functionality is organized into focused services:

#### VirtualRouteService
- Central service for virtual route processing
- Route detection and matching
- Virtual page generation
- Placeholder processing

#### PlaceholderService
- Handles placeholder replacement
- Type-specific formatting
- Extensible placeholder system

#### UrlGenerationService
- URL generation from models
- URL parsing and validation
- Site-aware URL building

#### SiteConfigurationService
- Multi-site support
- Site detection and configuration
- Root page resolution

## Data Flow Architecture

### Request Processing Pipeline

```mermaid
graph TD
    A[HTTP Request] --> B[VirtualRouteHandler]
    B --> C{Virtual Route?}
    C -->|Yes| D[Modify Request]
    C -->|No| E[Normal Processing]
    D --> F[PageResolver]
    F --> G[Template Page Loaded]
    G --> H[Event Listeners]
    H --> I[Data Processors]
    I --> J[Content Generation]
    J --> K[Placeholder Processing]
    K --> L[Response]
    E --> M[Standard TYPO3]
```

### Data Processing Layers

#### 1. Request Layer
- HTTP request interception
- Route pattern matching
- Request modification

#### 2. Resolution Layer
- Template page loading
- Virtual route context setup
- Page data preparation

#### 3. Processing Layer
- Flight data integration
- Content element processing
- Template variable assignment

#### 4. Rendering Layer
- Fluid template rendering
- Placeholder replacement
- Final content generation

## Component Interactions

### Core Component Relationships

```mermaid
graph LR
    A[VirtualRouteHandler] --> B[VirtualRouteService]
    B --> C[FlightRouteRepository]
    B --> D[TemplateResolutionService]
    E[FlightRouteProcessor] --> B
    E --> F[PlaceholderService]
    G[EventListeners] --> B
    H[UrlGenerationService] --> C
    I[SiteConfigurationService] --> J[SiteFinder]
```

### Service Dependencies

#### VirtualRouteService Dependencies:
- `TemplateResolutionService`: Template page resolution
- `FlightRouteRepository`: Route data access
- `PlaceholderService`: Content processing

#### FlightRouteProcessor Dependencies:
- `VirtualRouteService`: Virtual route detection
- `FlightRouteRepository`: Route data loading
- `PlaceholderService`: Content processing

#### UrlGenerationService Dependencies:
- `SiteConfigurationService`: Site context
- `FlightRouteRepository`: Route data

## Design Patterns

### 1. Repository Pattern

Domain repositories provide data access abstraction:

```php
interface FlightRouteRepositoryInterface
{
    public function findAllActive(): array;
    public function findByRouteSlug(string $slug): ?FlightRoute;
    public function findByLandingPage(int $landingPageUid): array;
}
```

### 2. Service Locator Pattern

Services are registered in `Services.yaml` and injected via DI:

```yaml
services:
  Bgs\LandingPages\Service\VirtualRouteService:
    arguments:
      $templateResolutionService: '@Bgs\LandingPages\Service\TemplateResolutionService'
```

### 3. Strategy Pattern

Different placeholder processing strategies based on content type:

```php
class PlaceholderService
{
    protected function getPlaceholderValue(string $placeholder, array $data): string
    {
        return match($placeholder) {
            'origin' => $this->formatLocationName($data, 'origin'),
            'destination' => $this->formatLocationName($data, 'destination'),
            'origin_code' => $data['origin_code'] ?? '',
            default => $this->getCustomPlaceholder($placeholder, $data)
        };
    }
}
```

### 4. Observer Pattern

Event listeners observe TYPO3 events for processing:

```php
class VirtualContentProcessingListener
{
    public function __invoke(AfterCacheableContentIsGeneratedEvent $event): void
    {
        if ($this->virtualRouteService->isVirtualRoute()) {
            $this->processVirtualContent($event);
        }
    }
}
```

### 5. Factory Pattern

Virtual route context creation:

```php
class VirtualRouteContext
{
    public static function createVirtual(
        array $landingPage,
        array $templatePage,
        array $flightRoute,
        string $originalPath,
        string $routeSlug,
        int $templatePageUid,
        string $templatePagePath
    ): self {
        // Factory method implementation
    }
}
```

## Caching Strategy

### Cache Considerations

#### Virtual Route Caching
- Each virtual route gets unique cache entry
- Cache key includes route slug parameter
- Template page cache is shared across routes

#### Content Caching
- Processed content is cached per route
- Placeholder processing results are cached
- Cache invalidation on route changes

#### Implementation
```php
// Unique cache parameter for each route
$cacheParam = 'route=' . urlencode($routeSlug);
$newQuery = $existingQuery ? $existingQuery . '&' . $cacheParam : $cacheParam;
```

## Security Considerations

### Input Validation
- Route slug validation against database
- Path traversal prevention
- SQL injection protection via QueryBuilder

### Access Control
- Standard TYPO3 page access controls apply
- Template page access restrictions
- Backend user permissions for route management

### Content Security
- Placeholder content is escaped
- No direct user input in placeholders
- Template content follows TYPO3 security practices

## Performance Optimizations

### Database Optimization
- Indexed route slug lookups
- Efficient query patterns
- Connection pooling via TYPO3

### Memory Management
- Lazy loading of route data
- Efficient object creation
- Proper resource cleanup

### Caching Optimization
- Aggressive caching of processed content
- Cache warming for popular routes
- Efficient cache invalidation

## Extensibility Points

### Custom Services
Replace or extend core services via DI configuration:

```yaml
services:
  Bgs\LandingPages\Service\PlaceholderService:
    class: Your\Extension\Service\CustomPlaceholderService
```

### Event Listeners
Add custom processing via event listeners:

```yaml
Your\Extension\EventListener\CustomListener:
  tags:
    - name: event.listener
      identifier: 'custom-processing'
      event: TYPO3\CMS\Frontend\Event\AfterCacheableContentIsGeneratedEvent
```

### Data Processors
Custom data processors for specialized content:

```php
class CustomDataProcessor implements DataProcessorInterface
{
    public function process(/* ... */): array
    {
        // Custom processing logic
    }
}
```

### ViewHelpers
Custom Fluid ViewHelpers for template functionality:

```php
class CustomViewHelper extends AbstractViewHelper
{
    public function render(): string
    {
        // Custom rendering logic
    }
}
```

## Migration Considerations

### From Content Elements to Page Types
The extension evolved from content element-based to page type-based architecture:

#### Legacy Support
- Backward compatibility for existing content elements
- Migration tools for converting old configurations
- Gradual migration path

#### Benefits of Page Types
- Better integration with TYPO3 core
- Improved performance and caching
- Enhanced backend user experience
- Better SEO and URL management

## Future Architecture Considerations

### Scalability Improvements
- Route data partitioning for large datasets
- Distributed caching strategies
- API-based route management

### Integration Enhancements
- Real-time flight data integration
- External API connectivity
- Microservice architecture support

### Performance Optimizations
- Advanced caching strategies
- CDN integration
- Edge computing support

---

This architecture provides a solid foundation for scalable, maintainable flight landing page functionality while maintaining TYPO3 best practices and extensibility.
