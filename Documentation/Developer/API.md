# API Reference

## Overview

This document provides comprehensive API reference for the Flight Landing Pages extension, including all public methods, interfaces, and extension points.

## Core Services

### VirtualRouteService

**Namespace**: `Bgs\LandingPages\Service\VirtualRouteService`

Central service for virtual route processing and management.

#### Public Methods

##### `detectVirtualRoute(string $path, Site $site): ?array`

Detects if a given path matches a virtual route pattern.

**Parameters**:
- `$path` (string): The URL path to check
- `$site` (Site): TYPO3 site instance

**Returns**: Array with route data or null if no match

**Example**:
```php
$virtualRouteService = GeneralUtility::makeInstance(VirtualRouteService::class);
$routeData = $virtualRouteService->detectVirtualRoute('/flights/ber-sof', $site);

if ($routeData) {
    // Virtual route detected
    $landingPage = $routeData['landingPage'];
    $flightRoute = $routeData['flightRoute'];
}
```

##### `isVirtualRoute(): bool`

Checks if the current request is processing a virtual route.

**Returns**: Boolean indicating virtual route status

**Example**:
```php
if ($virtualRouteService->isVirtualRoute()) {
    // Process virtual route specific logic
}
```

##### `getCurrentVirtualRoute(): ?array`

Gets the current virtual route data if available.

**Returns**: Array with current virtual route data or null

**Example**:
```php
$routeData = $virtualRouteService->getCurrentVirtualRoute();
if ($routeData) {
    $flightRoute = $routeData['flightRoute'];
    $templatePage = $routeData['templatePage'];
}
```

##### `generateVirtualPage(array $templatePage, array $flightRoute, string $originalPath): array`

Generates virtual page data from template page and flight route.

**Parameters**:
- `$templatePage` (array): Template page data
- `$flightRoute` (array): Flight route data
- `$originalPath` (string): Original request path

**Returns**: Array with virtual page data

##### `processPlaceholdersInContent(string $content, array $flightRoute): string`

Processes placeholders in content with flight route data.

**Parameters**:
- `$content` (string): Content with placeholders
- `$flightRoute` (array): Flight route data for replacement

**Returns**: Processed content with placeholders replaced

**Example**:
```php
$content = "Flight from [_origin_] to [_destination_]";
$processed = $virtualRouteService->processPlaceholdersInContent($content, $flightRoute);
// Result: "Flight from Berlin to Sofia"
```

##### `setVirtualRoute(array $virtualRouteData): void`

Sets the current virtual route data.

**Parameters**:
- `$virtualRouteData` (array): Virtual route data to store

##### `clearVirtualRoute(): void`

Clears the current virtual route data.

### PlaceholderService

**Namespace**: `Bgs\LandingPages\Service\PlaceholderService`

Handles placeholder replacement in content with flight-specific formatting.

#### Public Methods

##### `replacePlaceholders(string $content, array $flightRouteData): string`

Replaces all placeholders in content with flight route data.

**Parameters**:
- `$content` (string): Content containing placeholders
- `$flightRouteData` (array): Flight route data

**Returns**: Content with placeholders replaced

**Example**:
```php
$placeholderService = GeneralUtility::makeInstance(PlaceholderService::class);
$content = "Book your flight from [_origin_] to [_destination_] for [_price_] [_currency_]";
$processed = $placeholderService->replacePlaceholders($content, $flightRouteData);
```

##### `getAvailablePlaceholders(): array`

Gets list of all available placeholders.

**Returns**: Array of placeholder names

**Example**:
```php
$placeholders = $placeholderService->getAvailablePlaceholders();
// Returns: ['origin', 'destination', 'origin_code', 'destination_code', 'price', 'currency', ...]
```

#### Supported Placeholders

| Placeholder | Description | Example Output |
|-------------|-------------|----------------|
| `[_origin_]` | Origin name (formatted by type) | "Berlin" or "Berlin Brandenburg Airport (BER)" |
| `[_destination_]` | Destination name (formatted by type) | "Sofia" or "Sofia Airport (SOF)" |
| `[_origin_code_]` | Origin code | "BER" |
| `[_destination_code_]` | Destination code | "SOF" |
| `[_origin_type_]` | Origin type | "airport", "city", "country" |
| `[_destination_type_]` | Destination type | "airport", "city", "country" |
| `[_route_slug_]` | Route URL slug | "ber-sof" |
| `[_price_]` | Flight price | "299" |
| `[_currency_]` | Price currency | "EUR" |
| `[_airline_]` | Airline name | "Example Airlines" |

### UrlGenerationService

**Namespace**: `Bgs\LandingPages\Service\UrlGenerationService`

Handles URL generation and parsing for flight routes.

#### Public Methods

##### `generateRouteUrl(FlightRoute $flightRoute, array $landingPage, Site $site = null): string`

Generates URL for a flight route.

**Parameters**:
- `$flightRoute` (FlightRoute): Flight route model
- `$landingPage` (array): Landing page data
- `$site` (Site, optional): TYPO3 site instance

**Returns**: Generated URL string

**Example**:
```php
$urlService = GeneralUtility::makeInstance(UrlGenerationService::class);
$url = $urlService->generateRouteUrl($flightRoute, $landingPage, $site);
// Returns: "https://example.com/flights/ber-sof"
```

##### `parseVirtualRouteUrl(string $url): ?array`

Parses a virtual route URL into components.

**Parameters**:
- `$url` (string): URL to parse

**Returns**: Array with URL components or null if invalid

**Example**:
```php
$components = $urlService->parseVirtualRouteUrl('https://example.com/flights/ber-sof');
// Returns: ['landingPagePath' => '/flights', 'routeSlug' => 'ber-sof']
```

##### `buildRouteUrl(int $landingPageUid, string $routeSlug, Site $site = null): string`

Builds URL from landing page UID and route slug.

**Parameters**:
- `$landingPageUid` (int): Landing page UID
- `$routeSlug` (string): Route slug
- `$site` (Site, optional): TYPO3 site instance

**Returns**: Built URL string

### SiteConfigurationService

**Namespace**: `Bgs\LandingPages\Service\SiteConfigurationService`

Provides multi-site support and configuration management.

#### Public Methods

##### `getCurrentSite(): ?Site`

Gets the current TYPO3 site instance.

**Returns**: Site instance or null

##### `getSiteByRootPageId(int $rootPageId): ?Site`

Gets site by root page ID.

**Parameters**:
- `$rootPageId` (int): Root page ID

**Returns**: Site instance or null

##### `getAllSites(): array`

Gets all configured TYPO3 sites.

**Returns**: Array of Site instances

## Domain Models

### FlightRoute

**Namespace**: `Bgs\LandingPages\Domain\Model\FlightRoute`

Represents a flight route between two locations.

#### Properties

| Property | Type | Description |
|----------|------|-------------|
| `originCode` | string | Origin airport/city code |
| `originName` | string | Origin display name |
| `originType` | string | Origin type (airport/city/country) |
| `destinationCode` | string | Destination airport/city code |
| `destinationName` | string | Destination display name |
| `destinationType` | string | Destination type |
| `routeSlug` | string | URL slug for the route |
| `isActive` | bool | Route availability status |

#### Methods

All properties have corresponding getter and setter methods following the pattern:
- `getPropertyName(): type`
- `setPropertyName(type $value): void`

**Example**:
```php
$route = new FlightRoute();
$route->setOriginCode('BER');
$route->setOriginName('Berlin Brandenburg Airport');
$route->setOriginType('airport');
$route->setDestinationCode('SOF');
$route->setDestinationName('Sofia Airport');
$route->setDestinationType('airport');
$route->setRouteSlug('ber-sof');
$route->setIsActive(true);
```

### VirtualRouteContext

**Namespace**: `Bgs\LandingPages\Domain\Model\VirtualRouteContext`

Entity for managing virtual route state during request processing.

#### Static Factory Methods

##### `createVirtual(...): VirtualRouteContext`

Creates a virtual route context instance.

**Parameters**:
- `$landingPage` (array): Landing page data
- `$templatePage` (array): Template page data
- `$flightRoute` (array): Flight route data
- `$originalPath` (string): Original request path
- `$routeSlug` (string): Route slug
- `$templatePageUid` (int): Template page UID
- `$templatePagePath` (string): Template page path

**Returns**: VirtualRouteContext instance

## Repositories

### FlightRouteRepository

**Namespace**: `Bgs\LandingPages\Domain\Repository\FlightRouteRepository`

Repository for flight route data access.

#### Public Methods

##### `findAllActive(): array`

Finds all active flight routes.

**Returns**: Array of FlightRoute objects

##### `findByRouteSlug(string $routeSlug): ?FlightRoute`

Finds flight route by slug.

**Parameters**:
- `$routeSlug` (string): Route slug to search for

**Returns**: FlightRoute object or null

##### `findByLandingPage(int $landingPageUid): array`

Finds all routes for a specific landing page.

**Parameters**:
- `$landingPageUid` (int): Landing page UID

**Returns**: Array of FlightRoute objects

##### `findByOriginAndDestination(string $originCode, string $destinationCode): ?FlightRoute`

Finds route by origin and destination codes.

**Parameters**:
- `$originCode` (string): Origin code
- `destinationCode` (string): Destination code

**Returns**: FlightRoute object or null

## Data Processors

### FlightRouteProcessor

**Namespace**: `Bgs\LandingPages\DataProcessing\FlightRouteProcessor`

TypoScript data processor for Flight Landing Pages.

#### Configuration

```typoscript
page.10.dataProcessing {
    10 = Bgs\LandingPages\DataProcessing\FlightRouteProcessor
    10 {
        as = flightRouteData
    }
}
```

#### Processed Data Structure

```php
[
    'flightRouteData' => [
        'origin' => 'Berlin Brandenburg Airport',
        'originCode' => 'BER',
        'originType' => 'airport',
        'destination' => 'Sofia Airport',
        'destinationCode' => 'SOF',
        'destinationType' => 'airport',
        'routeSlug' => 'ber-sof',
        'price' => '299',
        'currency' => 'EUR',
        'airline' => 'Example Airlines',
        'flight' => [
            'duration' => '2h 30m',
            'departure' => '10:00',
            'arrival' => '12:30'
        ]
    ]
]
```

### DestinationPairsMenuProcessor

**Namespace**: `Bgs\LandingPages\DataProcessing\DestinationPairsMenuProcessor`

Data processor for destination menu content elements.

#### Configuration

```typoscript
tt_content.destinationpairsmenu_destinationpairsmenu.dataProcessing {
    10 = Bgs\LandingPages\DataProcessing\DestinationPairsMenuProcessor
    10 {
        as = destinations
        displayMode = list
        itemsPerPage = 20
    }
}
```

## ViewHelpers

### ReplacePlaceholderViewHelper

**Namespace**: `Bgs\LandingPages\ViewHelpers\ReplacePlaceholderViewHelper`

Fluid ViewHelper for placeholder replacement in templates.

#### Usage

```html
{namespace lp=Bgs\LandingPages\ViewHelpers}

<lp:replacePlaceholder content="{templateContent}" data="{flightRouteData}" />
```

#### Arguments

| Argument | Type | Required | Description |
|----------|------|----------|-------------|
| `content` | string | Yes | Content with placeholders |
| `data` | array | Yes | Flight route data for replacement |

## Event Listeners

### Custom Event Listener Registration

Register custom event listeners in `Services.yaml`:

```yaml
services:
  Your\Extension\EventListener\CustomListener:
    tags:
      - name: event.listener
        identifier: 'your-custom-listener'
        event: TYPO3\CMS\Frontend\Event\AfterCacheableContentIsGeneratedEvent
        method: 'handleEvent'
```

### Available Events

#### BeforePageIsResolvedEvent
- **Purpose**: Virtual route detection
- **Listener**: `VirtualRouteDetectionListener`

#### AfterPageWithRootLineIsResolvedEvent
- **Purpose**: Page replacement with virtual data
- **Listener**: `VirtualPageReplacementListener`

#### AfterCacheableContentIsGeneratedEvent
- **Purpose**: Content post-processing
- **Listener**: `VirtualContentProcessingListener`

#### ModifyPageLayoutContentEvent
- **Purpose**: Backend preview enhancement
- **Listener**: `PagePreviewEventListener`

## CLI Commands

### UpdateSlugCommand

**Command**: `landingpages:updateslug`

Updates route slugs based on current configuration.

```bash
ddev typo3cms landingpages:updateslug [options]
```

**Options**:
- `--dry-run`: Show what would be updated without making changes
- `--force`: Force update even if slugs exist

### UpdateChildSlugsCommand

**Command**: `landingpages:updatechildslugs`

Updates child page slugs when parent pages change.

```bash
ddev typo3cms landingpages:updatechildslugs [parent-page-uid]
```

## Extension Points

### Custom Placeholder Processing

Extend placeholder functionality by overriding the PlaceholderService:

```php
class CustomPlaceholderService extends PlaceholderService
{
    protected function getPlaceholderValue(string $placeholder, array $flightRoute): string
    {
        return match($placeholder) {
            'custom_field' => $this->getCustomValue($flightRoute),
            'api_data' => $this->fetchApiData($flightRoute),
            default => parent::getPlaceholderValue($placeholder, $flightRoute)
        };
    }
    
    private function getCustomValue(array $flightRoute): string
    {
        // Custom logic
        return 'custom value';
    }
}
```

### Custom Route Detection

Override virtual route detection logic:

```php
class CustomVirtualRouteService extends VirtualRouteService
{
    public function detectVirtualRoute(string $path, Site $site): ?array
    {
        // Custom detection logic
        if ($this->isSpecialRoute($path)) {
            return $this->handleSpecialRoute($path, $site);
        }
        
        return parent::detectVirtualRoute($path, $site);
    }
}
```

### Custom Data Processing

Create specialized data processors:

```php
class ApiDataProcessor implements DataProcessorInterface
{
    public function process(
        ContentObjectRenderer $cObj,
        array $contentObjectConfiguration,
        array $processorConfiguration,
        array $processedData
    ): array {
        $flightData = $processedData['flightRouteData'] ?? [];
        
        if (!empty($flightData)) {
            // Fetch real-time data from API
            $apiData = $this->fetchFlightData($flightData);
            $processedData['apiFlightData'] = $apiData;
        }
        
        return $processedData;
    }
}
```

## Error Handling

### Exception Classes

The extension defines custom exceptions for specific error conditions:

```php
namespace Bgs\LandingPages\Exception;

class VirtualRouteException extends \Exception {}
class TemplateNotFoundException extends VirtualRouteException {}
class RouteNotFoundException extends VirtualRouteException {}
```

### Error Handling Patterns

```php
try {
    $routeData = $virtualRouteService->detectVirtualRoute($path, $site);
} catch (VirtualRouteException $e) {
    // Handle virtual route specific errors
    $this->logger->error('Virtual route error: ' . $e->getMessage());
    return null;
}
```

---

This API reference provides comprehensive documentation for extending and integrating with the Flight Landing Pages extension.
