# Automatic Cache Clearing

The Flight Landing Pages extension automatically clears template page caches when destination pairs (flight routes) are edited.

## How It Works

The system automatically clears template page caches in two scenarios:

### Individual Route Editing

When a destination pair is edited in the TYPO3 backend, the system automatically:

1. **Detects the change** - The `DataHandlerHook` intercepts flight route save operations
2. **Identifies the landing page** - Uses the route's `pid` field to find the parent landing page
3. **Resolves the template page** - Uses the route's `originType` and `destinationType` to find the appropriate template page:
   - First checks for a specific template mapping for the type combination (e.g., Airport → Country)
   - Falls back to the default template page if no specific mapping exists
4. **Clears the cache** - Clears the frontend cache for the resolved template page

### CSV Import

When destination pairs are imported via CSV, the system automatically:

1. **Processes the import** - Imports/updates all destination pairs from the CSV file
2. **Tracks changes** - Counts new records vs. updated records
3. **Clears all template caches** - Clears cache for all template pages related to the landing page:
   - Default template page
   - All specific template mapping pages
4. **Shows detailed feedback** - Displays import statistics and cache clearing confirmation

## Example Scenarios

### Scenario 1: Specific Template Mapping
- **Route**: IST (Airport) → BG (Country) 
- **Landing Page**: `/flights/poleti/` (UID: 123)
- **Template Mapping**: Airport → Country maps to Template Page UID 456
- **Result**: Cache for Template Page 456 is cleared

### Scenario 2: Default Template
- **Route**: BER (Airport) → SOF (Airport)
- **Landing Page**: `/flights/poleti/` (UID: 123)
- **Template Mapping**: No specific mapping for Airport → Airport
- **Default Template**: Template Page UID 789 (set in landing page configuration)
- **Result**: Cache for Template Page 789 is cleared

### Scenario 3: CSV Import
- **Action**: Import CSV with 50 destination pairs (30 new, 20 updates)
- **Landing Page**: `/flights/poleti/` (UID: 123)
- **Template Pages**: Default (UID: 789) + 3 specific mappings (UIDs: 456, 567, 678)
- **Result**:
  - All 50 destination pairs imported/updated
  - Cache cleared for 4 template pages
  - User sees: "Import completed successfully: 30 new destination pair(s) imported, 20 existing destination pair(s) updated. Cache cleared for 4 template page(s)."

## Technical Implementation

The automatic cache clearing is implemented in:

**Individual Route Editing:**
- **File**: `Classes/Hooks/DataHandlerHook.php`
- **Method**: `handleFlightRouteUpdate()`
- **Hook**: `processDatamap_afterDatabaseOperations`
- **Trigger**: Any save operation on `tx_landingpages_domain_model_flightroute` table

**CSV Import:**
- **File**: `Classes/Controller/Backend/CsvExportController.php`
- **Method**: `clearTemplatePagesCache()`
- **Trigger**: After successful CSV import completion

### Dependencies

The implementation uses these services:
- `TemplateResolutionService` - To resolve the correct template page
- `CacheManager` - To clear the frontend cache
- `ConnectionPool` - To query flight route data

### Cache Clearing Strategy

The system clears:
1. **Page cache** - Using tag `pageId_{templatePageUid}`
2. **Hash cache** - General cache that might contain related data

## Benefits

- **Automatic** - No manual cache clearing required
- **Targeted** - Only clears cache for affected template pages (individual edits) or all related template pages (CSV import)
- **Efficient** - Uses TYPO3's tag-based cache clearing
- **Safe** - Errors don't break the save operation
- **Informative** - CSV import provides detailed feedback about what was imported and cached cleared
- **Comprehensive** - CSV import clears all template page caches to ensure consistency

## Configuration

No additional configuration is required. The feature works automatically when:
- The extension is installed and active
- Flight routes are properly configured with landing pages
- Template pages are set up (either default or with specific mappings)

## Troubleshooting

If cache clearing doesn't work:

1. **Check logs** - Look for errors in TYPO3 system logs
2. **Verify template configuration** - Ensure template pages are properly configured
3. **Test manually** - Use the manual cache clearing buttons in the backend
4. **Clear all caches** - Run `ddev exec vendor/bin/typo3 cache:flush`

## Related Features

- [Template Page Mappings](TemplatePageMappings.md)
- [Manual Cache Clearing](ManualCacheClearing.md)
- [Backend Cache Buttons](BackendCacheButtons.md)
