# Production Fix: Data Processor Error

## Problem
Error in production: `Processor class or service name "Bgs\LandingPages\DataProcessing\VirtualRouteDataProcessor" does not exist!`

This indicates that somewhere in the TypoScript configuration, there's still a reference to the `VirtualRouteDataProcessor` that we removed from the global configuration.

## Root Cause Analysis

The error suggests one of these scenarios:
1. **Cached TypoScript** - Old configuration still cached
2. **Site-specific TypoScript** - Your site's TypoScript references this processor
3. **Template-specific configuration** - A template includes this processor
4. **Database TypoScript records** - TypoScript stored in database records

## Immediate Fix Applied

I've disabled the TypoScript inclusion again to prevent any interference:

**File**: `ext_localconf.php`
```php
// Temporarily disabled again due to production errors
// \TYPO3\CMS\Core\Utility\ExtensionManagementUtility::addTypoScriptSetup(
//     '@import "EXT:landing-pages/Configuration/TypoScript/setup.typoscript"'
// );
```

## Deployment Instructions

### 1. Upload Modified File
- `packages/landing-pages/ext_localconf.php`

### 2. Clear ALL Caches Aggressively
```bash
# Via CLI (if available)
typo3cms cache:flush

# Via Install Tool
# Admin Tools > Maintenance > Flush TYPO3 and PHP Caches
# Also clear: "Clear all cache"

# Manual cache clearing (if needed)
rm -rf typo3temp/var/cache/*
rm -rf var/cache/* (TYPO3 v12)
```

### 3. Check for Site-Specific TypoScript

Look for references to `VirtualRouteDataProcessor` in:

#### A. TypoScript Templates (Backend)
1. Go to **Web > Template**
2. Select your root page
3. Click **Info/Modify**
4. Search for `VirtualRouteDataProcessor` in:
   - Setup field
   - Constants field

#### B. TypoScript Files
Check your site's TypoScript files for:
```typoscript
# Look for lines like this:
page.10.dataProcessing {
    X = Bgs\LandingPages\DataProcessing\VirtualRouteDataProcessor
}

# Or:
dataProcessing {
    X = Bgs\LandingPages\DataProcessing\VirtualRouteDataProcessor
}
```

#### C. Database Records
Check if there are TypoScript records in the database:
```sql
SELECT * FROM sys_template WHERE config LIKE '%VirtualRouteDataProcessor%';
SELECT * FROM sys_template WHERE constants LIKE '%VirtualRouteDataProcessor%';
```

## Alternative Solutions

### Option 1: Create a Dummy Class (Quick Fix)
If you can't find the reference, create a dummy processor:

**File**: `packages/landing-pages/Classes/DataProcessing/VirtualRouteDataProcessor.php`
```php
<?php
namespace Bgs\LandingPages\DataProcessing;

use TYPO3\CMS\Frontend\ContentObject\ContentObjectRenderer;
use TYPO3\CMS\Frontend\ContentObject\DataProcessorInterface;

class VirtualRouteDataProcessor implements DataProcessorInterface
{
    public function process(
        ContentObjectRenderer $cObj,
        array $contentObjectConfiguration,
        array $processorConfiguration,
        array $processedData
    ): array {
        // Dummy processor - does nothing to prevent errors
        return $processedData;
    }
}
```

### Option 2: Remove Extension Completely (If Needed)
If the extension continues to cause issues:

1. **Deactivate extension**:
   - Admin Tools > Extensions
   - Deactivate "Landing Pages"

2. **Remove files** (if necessary):
   - Delete `packages/landing-pages/` directory

3. **Clear caches**

## Prevention Strategy

### For Future Extension Installation:

1. **Test in staging first** - Always test extensions in a staging environment
2. **Backup before installation** - Create full backup before installing extensions
3. **Check TypoScript conflicts** - Review extension TypoScript before installation
4. **Gradual activation** - Enable extension features gradually

### For This Extension:

1. **Manual TypoScript integration** - Instead of automatic inclusion, manually add needed TypoScript
2. **Conditional loading** - Only load TypoScript when actually needed
3. **Site-specific configuration** - Add extension TypoScript to site templates, not globally

## Current State

### ✅ Safe State:
- Extension installed but TypoScript disabled
- No interference with existing site
- Backend functionality available (if needed)
- Database schema intact

### ❌ Not Working:
- Extension frontend functionality
- Content elements
- Virtual routes

## Next Steps

1. **Verify site stability** after applying this fix
2. **Identify the source** of the VirtualRouteDataProcessor reference
3. **Decide on extension usage**:
   - Remove completely if not needed
   - Configure properly if needed
   - Keep disabled if uncertain

## Monitoring

After deployment:
- ✅ Check that `/nachalo` page works without errors
- ✅ Verify other site pages work normally
- ✅ Monitor error logs for any remaining issues
- ✅ Test site functionality thoroughly

## Contact Information

If you need help identifying where the `VirtualRouteDataProcessor` reference is coming from:

1. **Check TypoScript templates** in TYPO3 backend
2. **Search site files** for "VirtualRouteDataProcessor"
3. **Check database** for TypoScript records
4. **Provide specific error details** if issues persist

This fix should immediately resolve the production error by preventing the extension from loading any TypoScript that could reference the problematic data processor.
